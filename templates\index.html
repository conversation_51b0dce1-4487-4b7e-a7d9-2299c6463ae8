<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>涟源市妇幼保健院分级诊疗双向转诊服务平台</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-overlay hidden">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 提示框 -->
    <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-hospital me-2"></i>涟源市妇幼保健院分级诊疗双向转诊服务平台
            </a>
            <div class="d-flex align-items-center text-white hidden" id="userInfo">
                <span class="me-3" id="userName"></span>
                <button class="btn btn-light btn-sm" onclick="logout()">退出</button>
            </div>
        </div>
    </nav>

    <!-- 登录表单 -->
    <div id="loginForm" class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h2 class="text-center mb-4">登录</h2>
                        <form onsubmit="handleLogin(event)">
                            <div class="mb-3">
                                <input type="text" 
                                       id="username" 
                                       class="form-control" 
                                       placeholder="用户名" 
                                       required>
                            </div>
                            <div class="mb-3">
                                <input type="password" 
                                       id="password" 
                                       class="form-control" 
                                       placeholder="密码" 
                                       required>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">记住登录</label>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">登录</button>
                        </form>
                        <p class="text-center mt-3">
                            还没有账号？<a href="#" onclick="switchToRegister()">立即注册</a>
                        </p>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <p class="text-muted mb-1">涟源市总医院妇幼健康管理中心</p>
                    <p class="text-muted">（涟源市妇幼保健院）</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册表单 -->
    <div id="registerForm" class="container mt-5 hidden">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h2 class="text-center mb-4">注册</h2>
                        <form onsubmit="handleRegister(event)" novalidate>
                            <div class="mb-3">
                                <input type="text" 
                                       name="username" 
                                       class="form-control" 
                                       placeholder="注册账号" 
                                       required>
                            </div>
                            <div class="mb-3">
                                <input type="password" 
                                       name="password" 
                                       class="form-control" 
                                       placeholder="密码" 
                                       required>
                            </div>
                            <div class="mb-3">
                                <input type="text" 
                                       name="fullName" 
                                       class="form-control" 
                                       placeholder="姓名" 
                                       required>
                            </div>
                            <div class="mb-3">
                                <input type="tel" 
                                       name="phone" 
                                       class="form-control" 
                                       placeholder="电话" 
                                       pattern="^1[3-9]\d{9}$" 
                                       title="请输入有效的手机号码"
                                       required>
                            </div>
                            <div class="mb-3">
                                <input type="text" 
                                       name="town" 
                                       class="form-control" 
                                       placeholder="所属乡镇" 
                                       required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">注册</button>
                        </form>
                        <p class="text-center mt-3">
                            已有账号？<a href="#" onclick="switchToLogin()">立即登录</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="mainContent" class="container mt-4 hidden">
        <!-- 仪表盘 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5><i class="fas fa-user-clock me-2"></i>待处理转诊</h5>
                        <h2 class="mt-2" id="pendingCount">0 例</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5><i class="fas fa-check-circle me-2"></i>已处理病例</h5>
                        <h2 class="mt-2" id="processedCount">0 例</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- 转诊列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center flex-wrap gap-2">
                <h5 class="mb-0"><i class="fas fa-list-ul me-2"></i>转诊申请列表</h5>
                <button class="btn btn-primary" onclick="showNewReferralForm()">
                    <i class="fas fa-plus me-2"></i>新建转诊
                </button>
            </div>
            <div class="card-body p-0">
                <!-- 在小屏幕上显示的卡片式列表 -->
                <div class="d-md-none">
                    <div id="referralCardList" class="list-group list-group-flush">
                        <!-- 卡片将由 JavaScript 动态生成 -->
                    </div>
                </div>
                
                <!-- 在中等及以上屏幕显示的表格 -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>转诊时间</th>
                                <th>患者姓名</th>
                                <th>来源医院</th>
                                <th>目标医院</th>
                                <th>转诊医生</th>
                                <th>联系电话</th>
                                <th>状态</th>
                                <th>操作</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody id="referralTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 管理员功能区 -->
        <div id="adminPanel" class="card mt-4 d-none">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users-cog me-2"></i>用户管理</h5>
                <button class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-user-plus me-2"></i>创建新用户
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>医院/机构</th>
                                <th>角色</th>
                                <th>联系电话</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 新建转诊表单 -->
    <div id="referralForm" class="modal fade">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-file-medical me-2"></i>新建转诊申请</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form onsubmit="handleNewReferral(event)" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">患者姓名</label>
                                <input type="text" name="patientName" class="form-control" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">年龄</label>
                                <input type="number" name="patientAge" class="form-control" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">性别</label>
                                <select name="patientGender" class="form-select" required>
                                    <option value="女" selected>女</option>
                                    <option value="男">男</option>
                                </select>
                            </div>
                            <!-- 医院选择 - 只对基层医院显示 -->
                            <div class="col-12" id="hospitalSelection" style="display: none;">
                                <label class="form-label">转诊目标医院 <span class="text-danger">*</span></label>
                                <select name="toHospital" id="toHospital" class="form-select" required>
                                    <option value="">请选择医院</option>
                                </select>
                                <small class="text-muted">请选择患者需要转诊的目标医院</small>
                            </div>
                            <div class="col-12">
                                <label class="form-label">备注说明</label>
                                <textarea name="notes" class="form-control" rows="3"
                                          placeholder="请在此处填写患者情况、转诊原因等信息"></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">上传医疗截图</label>
                                <input type="file" name="medicalImage" class="form-control" accept="image/*">
                                <small class="text-muted">可以上传检查报告、病历等相关图片</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>提交申请
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 创建用户模态框 -->
    <div class="modal fade" id="createUserModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>创建新用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createUserForm" onsubmit="handleCreateUser(event)">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" name="username" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" name="password" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" name="fullName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">医院/机构名称</label>
                            <input type="text" name="hospital_name" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">联系电话</label>
                            <input type="text" name="phone" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select name="role" class="form-select" required>
                                <option value="hospital">上级医院</option>
                                <option value="town">基层医疗机构</option>
                                <option value="admin">系统管理员</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">创建</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-edit me-2"></i>编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editUserForm" onsubmit="handleUpdateUser(event)">
                    <input type="hidden" name="userId" id="editUserId">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" name="username" id="editUsername" class="form-control" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">新密码 (留空则不修改)</label>
                            <input type="password" name="password" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" name="fullName" id="editFullName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">医院/机构名称</label>
                            <input type="text" name="hospital_name" id="editHospitalName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">联系电话</label>
                            <input type="text" name="phone" id="editPhone" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select name="role" id="editRole" class="form-select" required>
                                <option value="hospital">上级医院</option>
                                <option value="town">基层医疗机构</option>
                                <option value="admin">系统管理员</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js', v=current_time) }}"></script>
</body>
</html>