#!/usr/bin/env python3
"""
WSGI入口文件 - 生产环境使用
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('FLASK_ENV', 'production')

# 导入应用
from app import create_app, db

# 创建应用实例
application = create_app('production')

# 初始化数据库
with application.app_context():
    try:
        db.create_all()
        print("Database tables created successfully")
    except Exception as e:
        print(f"Database initialization error: {e}")

if __name__ == "__main__":
    # 开发环境直接运行
    application.run(host='0.0.0.0', port=5000, debug=False)
