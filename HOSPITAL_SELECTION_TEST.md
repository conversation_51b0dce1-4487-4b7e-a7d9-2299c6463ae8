# 医院选择功能测试指南

## 测试前准备

### 1. 运行数据库迁移
首先运行迁移脚本来更新数据库：
```bash
python migrate_hospitals.py
```

### 2. 启动应用
```bash
python app.py
```

### 3. 测试账户
迁移脚本会自动创建以下医院管理员账户：

| 医院 | 用户名 | 密码 | 角色 |
|------|--------|------|------|
| 涟源市妇幼保健院 | maternity_admin | 123456 | hospital |
| 涟源市人民医院 | peoples_admin | 123456 | hospital |
| 涟源市中医院 | tcm_admin | 123456 | hospital |

## 功能测试步骤

### 测试1：基层医院创建转诊申请

1. **登录基层医院账户**
   - 使用任何 `role='town'` 的账户登录
   - 例如：通过注册功能创建一个基层医院账户

2. **创建转诊申请**
   - 点击"新建转诊"按钮
   - 验证是否显示"转诊目标医院"选择框
   - 选择框应包含3个选项：
     - 涟源市妇幼保健院
     - 涟源市人民医院
     - 涟源市中医院

3. **填写转诊信息**
   - 患者姓名：测试患者
   - 年龄：30
   - 性别：女
   - 目标医院：选择"涟源市妇幼保健院"
   - 备注：测试转诊申请

4. **提交申请**
   - 点击"提交申请"
   - 验证提交成功提示
   - 检查转诊列表中是否显示新记录

### 测试2：验证转诊列表显示

1. **检查表格视图（桌面端）**
   - 验证"目标医院"列是否正确显示
   - 应显示为蓝色标签：涟源市妇幼保健院

2. **检查卡片视图（移动端）**
   - 调整浏览器窗口到移动端尺寸
   - 验证卡片中是否显示目标医院信息

### 测试3：上级医院接收转诊

1. **登录妇幼保健院账户**
   - 用户名：maternity_admin
   - 密码：123456

2. **查看转诊列表**
   - 验证只能看到发送给"涟源市妇幼保健院"的转诊申请
   - 不应该看到发送给其他医院的申请

3. **处理转诊申请**
   - 点击"修改状态"按钮
   - 选择"已接诊"
   - 验证状态更新成功

### 测试4：其他医院权限验证

1. **登录人民医院账户**
   - 用户名：peoples_admin
   - 密码：123456

2. **验证权限隔离**
   - 应该看不到发送给妇幼保健院的转诊申请
   - 只能看到发送给人民医院的申请

### 测试5：转诊详情查看

1. **查看转诊详情**
   - 点击任意转诊记录的"查看"按钮
   - 验证详情页面显示：
     - 来源医院
     - 目标医院（带蓝色标签）
     - 转诊医生信息
     - 患者信息

### 测试6：创建多个不同目标医院的转诊

1. **创建发送给不同医院的转诊**
   - 创建发送给"涟源市人民医院"的转诊
   - 创建发送给"涟源市中医院"的转诊

2. **验证分发正确性**
   - 用不同医院账户登录
   - 验证每个医院只能看到发送给自己的转诊

## API测试

### 1. 获取医院列表
```bash
curl -X GET "http://localhost:5000/api/hospitals" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期响应：
```json
{
  "hospitals": [
    "涟源市妇幼保健院",
    "涟源市人民医院", 
    "涟源市中医院"
  ],
  "msg": "获取医院列表成功"
}
```

### 2. 创建转诊申请（JSON方式）
```bash
curl -X POST "http://localhost:5000/api/referrals" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "patient_info": {
      "name": "测试患者",
      "age": 25,
      "gender": "女"
    },
    "to_hospital": "涟源市妇幼保健院",
    "notes": "API测试转诊"
  }'
```

### 3. 创建转诊申请（表单方式）
```bash
curl -X POST "http://localhost:5000/api/referrals" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "patientName=测试患者2" \
  -F "patientAge=30" \
  -F "patientGender=男" \
  -F "toHospital=涟源市人民医院" \
  -F "notes=表单测试转诊"
```

## 预期结果验证

### ✅ 成功标准

1. **医院选择功能**
   - 基层医院能看到医院选择下拉框
   - 上级医院不显示医院选择框
   - 下拉框包含3个正确的医院选项

2. **权限控制**
   - 每个医院只能看到发送给自己的转诊
   - 基层医院能看到自己创建的所有转诊
   - 管理员能看到所有转诊

3. **数据显示**
   - 转诊列表正确显示目标医院
   - 转诊详情包含完整的医院信息
   - 移动端和桌面端显示一致

4. **API响应**
   - 医院列表API返回正确数据
   - 转诊创建API支持医院选择
   - 错误处理正确

### ❌ 常见问题排查

1. **医院选择框不显示**
   - 检查用户角色是否为'town'
   - 检查JavaScript控制台是否有错误

2. **医院列表为空**
   - 检查后端API是否正常
   - 检查数据库连接
   - 验证JWT token是否有效

3. **权限问题**
   - 检查用户角色配置
   - 验证医院名称匹配
   - 检查数据库中的医院名称

4. **转诊不显示**
   - 检查医院名称是否完全匹配
   - 验证数据库迁移是否成功
   - 检查转诊记录的to_hospital字段

## 性能测试

1. **并发创建转诊**
   - 同时创建多个转诊申请
   - 验证数据一致性

2. **大量数据显示**
   - 创建100+转诊记录
   - 测试列表加载性能

3. **移动端响应**
   - 在不同设备上测试
   - 验证触摸操作流畅性

## 回归测试

确保新功能不影响现有功能：

1. **用户登录/注册**
2. **转诊状态修改**
3. **备注编辑**
4. **图片上传**
5. **用户管理（管理员）**

## 测试完成检查清单

- [ ] 基层医院可以选择目标医院
- [ ] 上级医院只能看到发给自己的转诊
- [ ] 转诊列表正确显示目标医院
- [ ] 转诊详情包含完整信息
- [ ] API正确响应医院列表
- [ ] 移动端显示正常
- [ ] 权限控制正确
- [ ] 数据迁移成功
- [ ] 现有功能正常
- [ ] 性能表现良好
