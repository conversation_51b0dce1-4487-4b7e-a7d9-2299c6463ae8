let currentUser = null;
let loadingCount = 0;
let loadingIndicator = null;

// 修改基础URL常量为相对路径
const API_BASE_URL = ''; // 使用相对路径

// 科室联系人数据
const departmentContacts = {
    '产科': [
        { name: '张医生', phone: '13800138001' },
        { name: '李医生', phone: '13800138002' }
    ],
    '妇科': [
        { name: '王医生', phone: '13800138003' },
        { name: '赵医生', phone: '13800138004' }
    ],
    '儿科': [
        { name: '刘医生', phone: '13800138005' },
        { name: '陈医生', phone: '13800138006' }
    ],
    '保健科': [
        { name: '孙医生', phone: '13800138007' },
        { name: '周医生', phone: '13800138008' }
    ],
    '计划生育科': [
        { name: '吴医生', phone: '13800138009' },
        { name: '郑医生', phone: '13800138010' }
    ]
};

// 初始化加载指示器
function initLoadingIndicator() {
    loadingIndicator = document.getElementById('loadingIndicator');
    resetLoading();
}

// 重置加载状态
function resetLoading() {
    loadingCount = 0;
    if (loadingIndicator) {
        loadingIndicator.classList.add('hidden');
    }
}

// 显示加载指示器
function showLoading() {
    if (!loadingIndicator) {
        loadingIndicator = document.getElementById('loadingIndicator');
    }
    if (loadingIndicator) {
        loadingCount = Math.max(0, loadingCount + 1);
        loadingIndicator.classList.remove('hidden');
    }
}

// 隐藏加载指示器
function hideLoading() {
    if (!loadingIndicator) {
        loadingIndicator = document.getElementById('loadingIndicator');
    }
    if (loadingIndicator) {
        loadingCount = Math.max(0, loadingCount - 1);
        if (loadingCount === 0) {
            loadingIndicator.classList.add('hidden');
        }
    }
}

// 修改检查登录状态的函数
async function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        showLoginForm();
        return;
    }

    try {
        // 解析 token 中的用户信息
        try {
            currentUser = JSON.parse(atob(token.split('.')[1]));
        } catch (e) {
            console.error('Token parsing error:', e);
            throw new Error('Invalid token format');
        }

        // 验证 token 有效性
        const response = await fetch('/api/referrals', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Token invalid');
        }

        // 如果 token 有效，直接显示主内容
        showMainContent();
        const data = await response.json();
        if (data.referrals) {
            displayReferrals(data.referrals);
            updateStatistics(data.statistics);
        } else {
            console.error('Invalid response format:', data);
            throw new Error('Invalid response format');
        }
    } catch (error) {
        console.error('Auth error:', error);
        // 只有在 token 确实无效时才清除
        if (error.message === 'Token invalid') {
            localStorage.removeItem('token');
            localStorage.removeItem('rememberMe');
            showLoginForm();
        }
    }
}

// 显示提示信息
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.id = toastId;
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast, { autohide: true, delay: 3000 });
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// 表单验证
function validateForm(form) {
    let isValid = true;
    form.querySelectorAll('input, select, textarea').forEach(input => {
        if (input.hasAttribute('required') && !input.value) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        }
    });
    return isValid;
}

// 登录处理
async function handleLogin(event) {
    event.preventDefault();
    
    try {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;
        
        // 移除验证，直接发送请求
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                password,
                remember: rememberMe
            })
        });
        
        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.msg || '登录失败');
        }

        const data = await response.json();
        
        if (!data.access_token) {
            throw new Error('服务器响应中缺少访问令牌');
        }

        localStorage.setItem('token', data.access_token);
        if (rememberMe) {
            localStorage.setItem('rememberMe', 'true');
        }

        currentUser = JSON.parse(atob(data.access_token.split('.')[1]));
        
        await showMainContent();
        await loadReferrals();
        initAdminPanel();
        showToast('登录成功', 'success');
    } catch (error) {
        console.error('Login error:', error);
        showToast(error.message, 'danger');
    }
}

// 显示主要内容
function showMainContent() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const mainContent = document.getElementById('mainContent');
    const userNameElement = document.getElementById('userName');
    const userInfo = document.getElementById('userInfo');
    
    if (loginForm) loginForm.classList.add('hidden');
    if (registerForm) registerForm.classList.add('hidden');
    if (mainContent) mainContent.classList.remove('hidden');
    if (userInfo) userInfo.classList.remove('hidden');
    
    // 确保 currentUser 和必要的属性存在
    if (userNameElement && currentUser) {
        const username = currentUser.username || '用户';
        // 使用医院名称而不是角色文本
        const hospitalName = currentUser.hospital_name || 
                            (currentUser.role === 'town' ? '乡镇医院' : 
                             currentUser.role === 'hospital' ? '上级医院' : '');
        
        userNameElement.textContent = hospitalName ? `${username} (${hospitalName})` : username;
    }

    // 根据用户角色显示不同内容
    const newReferralButton = document.querySelector('button[onclick="showNewReferralForm()"]');
    if (newReferralButton) {
        if (currentUser && currentUser.role === 'town') {
            newReferralButton.style.display = 'block';
        } else {
            newReferralButton.style.display = 'none';
        }
    }
}

// 显示登录表单
function showLoginForm() {
    resetLoading(); // 重置加载状态
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const mainContent = document.getElementById('mainContent');
    const userInfo = document.getElementById('userInfo');
    
    if (loginForm) loginForm.classList.remove('hidden');
    if (registerForm) registerForm.classList.add('hidden');
    if (mainContent) mainContent.classList.add('hidden');
    if (userInfo) userInfo.classList.add('hidden');
}

// 加载转诊列表
async function loadReferrals() {
    const token = localStorage.getItem('token');
    if (!token) {
        showLoginForm();
        return;
    }

    showLoading();
    try {
        const response = await fetch('/api/referrals', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取转诊列表失败');
        }

        const data = await response.json();
        console.log('Received data:', data); // 添加调试日志
        
        if (data.referrals) {
            displayReferrals(data.referrals);
            updateStatistics(data.statistics);
            return data.referrals;
        } else {
            throw new Error('Invalid response format');
        }
    } catch (error) {
        console.error('Error loading referrals:', error);
        showToast(error.message, 'danger');
        return [];
    } finally {
        hideLoading();
    }
}

// 添加一个格式化时间的辅助函数
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            // 如果是已经格式化的字符串，直接返回
            return dateStr;
        }
        // 格式化为 YYYY-MM-DD HH:mm:ss
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (e) {
        console.error('Error formatting date:', e);
        return dateStr || '';
    }
}

// 修改显示转诊列表函数，优化移动端显示
function displayReferrals(referrals) {
    const tbody = document.getElementById('referralTableBody');
    const cardList = document.getElementById('referralCardList');
    
    if (tbody && cardList) {
        tbody.innerHTML = '';
        cardList.innerHTML = '';
        
        if (Array.isArray(referrals)) {
            referrals.forEach(r => {
                // 表格视图（桌面端）
                const row = document.createElement('tr');
                const createdAt = formatDateTime(r.created_at);
                
                // 生成操作按钮
                let operationButtons = `
                    <div class="btn-group">
                        <button class="btn btn-sm btn-primary" onclick="viewReferralDetail(${r.id})">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="editNotes(${r.id}, '${r.notes ? r.notes.replace(/'/g, "&apos;") : ''}')">
                            <i class="fas fa-edit"></i> 备注
                        </button>
                `;
                
                // 添加审核按钮（仅上级医院可见）
                if (currentUser && currentUser.role === 'hospital') {
                    operationButtons += `
                        <button type="button" class="btn btn-sm btn-info" onclick="showStatusOptions(${r.id})">
                            <i class="fas fa-edit me-1"></i>修改状态
                        </button>
                    `;
                }
                
                // 添加删除按钮
                if (currentUser && 
                    ((currentUser.role === 'town' && r.from_hospital === currentUser.hospital_name) || 
                     currentUser.role === 'admin') && 
                    r.status === 'pending') {
                    operationButtons += `
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteReferral(${r.id})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    `;
                }
                
                operationButtons += '</div>';
                
                // 备注信息（截取前20个字符）
                const notesShort = r.notes ? 
                    (r.notes.length > 20 ? r.notes.substring(0, 20) + '...' : r.notes) : 
                    '无';
                
                // 卡片视图（移动端）
                const card = `
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="card-subtitle text-muted">ID: ${r.id}</h6>
                                <span class="badge ${getStatusClass(r.status)}">${getStatusText(r.status)}</span>
                            </div>
                            <h5 class="card-title">${r.patient.name}</h5>
                            <p class="card-text">
                                <small class="text-muted">转诊时间：${createdAt}</small><br>
                                <small class="text-muted">来源医院：${r.from_hospital}</small><br>
                                <small class="text-muted">转诊医生：${r.doctor_name}</small><br>
                                <small class="text-muted">联系电话：${r.doctor_phone}</small>
                            </p>
                            <p class="card-text">
                                <small class="text-muted">备注：</small>
                                <a href="javascript:void(0)" 
                                   onclick="viewFullNotes('${r.notes ? r.notes.replace(/'/g, "&apos;").replace(/"/g, "&quot;") : '无'}')"
                                   class="notes-preview">
                                    ${notesShort}
                                </a>
                            </p>
                            <div class="d-flex flex-wrap gap-2">
                                ${operationButtons}
                            </div>
                        </div>
                    </div>
                `;
                
                cardList.insertAdjacentHTML('beforeend', card);
                
                // 表格行内容
                row.innerHTML = `
                    <td>${r.id}</td>
                    <td>${createdAt}</td>
                    <td>${r.patient.name}</td>
                    <td>${r.from_hospital}</td>
                    <td>${r.doctor_name}</td>
                    <td>${r.doctor_phone}</td>
                    <td><span class="badge ${getStatusClass(r.status)}">${getStatusText(r.status)}</span></td>
                    <td>${operationButtons}</td>
                    <td class="text-truncate" style="max-width: 200px;">
                        <a href="javascript:void(0)" 
                           onclick="viewFullNotes('${r.notes ? r.notes.replace(/'/g, "&apos;").replace(/"/g, "&quot;") : '无'}')">
                            ${notesShort}
                        </a>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    }
}

// 添加查看完整备注的功能
function viewFullNotes(notes) {
    const safeNotes = notes || '无备注';
    
    const modalHtml = `
        <div class="modal fade" id="fullNotesModal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-sticky-note me-2"></i>备注详情
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="p-3 bg-light rounded">
                            <p style="white-space: pre-wrap;">${safeNotes}</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除可能存在的旧模态框
    const oldModal = document.getElementById('fullNotesModal');
    if (oldModal) {
        oldModal.remove();
    }
    
    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('fullNotesModal'));
    modal.show();
}

// 显示状态选择对话框
function showStatusOptions(referralId) {
    const modalHtml = `
        <div class="modal fade" id="statusOptionsModal">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">修改转诊状态</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="reviewReferral(${referralId}, 'accepted')">
                                <i class="fas fa-check-circle me-2"></i>已接诊
                            </button>
                            <button class="btn btn-warning" onclick="reviewReferral(${referralId}, 'rejected')">
                                <i class="fas fa-times-circle me-2"></i>未接诊
                            </button>
                            <button class="btn btn-info" onclick="reviewReferral(${referralId}, 'transferred')">
                                <i class="fas fa-exchange-alt me-2"></i>已转院
                            </button>
                            <button class="btn btn-primary" onclick="reviewReferral(${referralId}, 'recovered')">
                                <i class="fas fa-heart me-2"></i>已康复
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除可能存在的旧模态框
    const oldModal = document.getElementById('statusOptionsModal');
    if (oldModal) {
        oldModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('statusOptionsModal'));
    
    // 设置点击状态按钮后自动关闭模态框
    document.querySelectorAll('#statusOptionsModal button').forEach(btn => {
        if (!btn.classList.contains('btn-close')) {
            btn.addEventListener('click', () => {
                modal.hide();
            });
        }
    });
    
    modal.show();
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'pending': return '待处理';
        case 'accepted': return '已接诊';
        case 'rejected': return '未接诊';
        case 'transferred': return '已转院';
        case 'recovered': return '已康复';
        default: return status;
    }
}

// 获取状态对应的样式类
function getStatusClass(status) {
    switch (status) {
        case 'pending':
            return 'bg-warning';
        case 'accepted':
            return 'bg-success';
        case 'rejected':
            return 'bg-danger';
        case 'transferred':
            return 'bg-info';
        case 'recovered':
            return 'bg-primary';
        default:
            return 'bg-secondary';
    }
}

// 修复统计数据显示函数
function updateStatistics(statistics) {
    if (!statistics) return;
    
    // 确保所有需要的属性存在，使用默认值0防止undefined
    const stats = {
        total: statistics.total || 0,
        pending: statistics.pending || 0,
        accepted: statistics.accepted || 0,
        rejected: statistics.rejected || 0
    };
    
    // 计算已处理数量（接受+拒绝+其他已完成状态）
    const processed = stats.total - stats.pending;
    
    // 更新统计卡片 - 添加空检查
    const pendingCountElement = document.getElementById('pendingCount');
    if (pendingCountElement) {
        pendingCountElement.textContent = `${stats.pending} 例`;
    }
    
    const processedCountElement = document.getElementById('processedCount');
    if (processedCountElement) {
        processedCountElement.textContent = `${processed} 例`;
    }
    
    const totalCountElement = document.getElementById('totalCount');
    if (totalCountElement) {
        totalCountElement.textContent = `${stats.total} 例`;
    }
    
    // 更新进度条 - 如果存在
    const pendingPercentage = stats.total > 0 ? Math.round((stats.pending / stats.total) * 100) : 0;
    const processedPercentage = stats.total > 0 ? Math.round((processed / stats.total) * 100) : 0;
    
    const pendingProgressElement = document.getElementById('pendingProgress');
    if (pendingProgressElement) {
        pendingProgressElement.style.width = `${pendingPercentage}%`;
        pendingProgressElement.setAttribute('aria-valuenow', pendingPercentage);
    }
    
    const pendingPercentageElement = document.getElementById('pendingPercentage');
    if (pendingPercentageElement) {
        pendingPercentageElement.textContent = `${pendingPercentage}%`;
    }
    
    const processedProgressElement = document.getElementById('processedProgress');
    if (processedProgressElement) {
        processedProgressElement.style.width = `${processedPercentage}%`;
        processedProgressElement.setAttribute('aria-valuenow', processedPercentage);
    }
    
    const processedPercentageElement = document.getElementById('processedPercentage');
    if (processedPercentageElement) {
        processedPercentageElement.textContent = `${processedPercentage}%`;
    }
}

// 显示新转诊申请表单
function showNewReferralForm() {
    const modal = new bootstrap.Modal(document.getElementById('referralForm'));
    modal.show();
}

// 更新科室联系人选项
function updateDepartmentContact(department) {
    const contactSelect = document.querySelector('select[name="departmentContact"]');
    if (!contactSelect) return;

    contactSelect.innerHTML = '<option value="">请选择科室联系人</option>';
    
    if (department && departmentContacts[department]) {
        departmentContacts[department].forEach(contact => {
            contactSelect.innerHTML += `
                <option value="${contact.name},${contact.phone}">
                    ${contact.name} (${contact.phone})
                </option>
            `;
        });
    }
}

// 修改处理新转诊申请函数以支持文件上传
async function handleNewReferral(event) {
    event.preventDefault();
    const form = event.target;
    
    if (!validateForm(form)) {
        showToast('请检查输入内容', 'danger');
        return;
    }
    
    showLoading();
    try {
        // 使用FormData处理包含文件的表单
        const formData = new FormData(form);
        
        // 添加患者信息
        const patientInfo = {
            name: form.patientName.value.trim(),
            age: form.patientAge.value.trim(),
            gender: form.patientGender.value,
        };
        
        formData.append('patient_info', JSON.stringify(patientInfo));
        formData.append('notes', form.notes.value.trim());
        
        const response = await fetch('/api/referrals', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
                // 注意：使用FormData时不要设置Content-Type
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.msg || '转诊申请提交失败');
        }
        
        showToast('转诊申请提交成功', 'success');
        form.reset();
        
        // 关闭表单并刷新列表
        const modal = bootstrap.Modal.getInstance(document.getElementById('referralForm'));
        modal.hide();
        
        await loadReferrals();
    } catch (error) {
        console.error('Error creating referral:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 修改查看转诊详情函数，移除患者信息中的病史
async function viewReferralDetail(referralId) {
    // 获取转诊记录详情
    const referrals = await loadReferrals();
    const referral = referrals.find(r => r.id === referralId);
    
    if (!referral) {
        showToast('加载转诊详情失败', 'danger');
        return;
    }
    
    // 格式化转诊记录创建时间
    const createdAt = formatDateTime(referral.created_at);
    
    // 获取状态类和文本
    const statusClass = getStatusClass(referral.status);
    const statusText = getStatusText(referral.status);
    
    // 格式化审核时间
    const reviewedAt = referral.reviewed_at ? formatDateTime(referral.reviewed_at) : '暂无';
    
    // 图片显示部分 - 修复图片路径问题
    let imageHtml = '';
    if (referral.image_path) {
        // 确保路径正确处理
        const imagePath = referral.image_path.startsWith('/') ? 
            referral.image_path : 
            `/${referral.image_path}`;
            
        imageHtml = `
            <div class="row mt-3">
                <div class="col-12">
                    <h5>医疗图片</h5>
                    <div class="text-center">
                        <img src="${imagePath}" class="img-thumbnail cursor-pointer" 
                             style="max-height: 200px;" 
                             onclick="viewFullImage('${imagePath}')" 
                             alt="医疗图片">
                        <div class="mt-2">
                            <button class="btn btn-sm btn-primary" onclick="viewFullImage('${imagePath}')">
                                <i class="fas fa-search-plus me-1"></i>查看大图
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        imageHtml = `
            <div class="row mt-3">
                <div class="col-12">
                    <h5>医疗图片</h5>
                    <p class="text-muted">无图片</p>
                </div>
            </div>
        `;
    }
    
    // 生成模态框内容
    const modalBody = `
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <h5>基本信息</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>ID</th>
                            <td>${referral.id}</td>
                        </tr>
                        <tr>
                            <th>创建时间</th>
                            <td>${createdAt}</td>
                        </tr>
                        <tr>
                            <th>状态</th>
                            <td><span class="badge ${statusClass}">${statusText}</span></td>
                        </tr>
                        <tr>
                            <th>审核时间</th>
                            <td>${reviewedAt}</td>
                        </tr>
                        <tr>
                            <th>审核人</th>
                            <td>${referral.reviewed_by || '暂无'}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>患者信息</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>姓名</th>
                            <td>${referral.patient.name}</td>
                        </tr>
                        <tr>
                            <th>性别</th>
                            <td>${referral.patient.gender}</td>
                        </tr>
                        <tr>
                            <th>年龄</th>
                            <td>${referral.patient.age}岁</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <h5>备注</h5>
                    <p>${referral.notes || '无'}</p>
                    ${currentUser && 
                      ((currentUser.role === 'town' && referral.from_hospital === currentUser.hospital_name) || 
                       currentUser.role === 'admin' || 
                       currentUser.role === 'hospital') ? 
                      `<button class="btn btn-sm btn-outline-secondary" onclick="editNotes(${referral.id}, '${referral.notes ? referral.notes.replace(/'/g, "&apos;").replace(/"/g, "&quot;") : ''}')">
                          <i class="fas fa-edit me-2"></i>编辑备注
                       </button>` : ''}
                </div>
            </div>
            ${imageHtml}
        </div>
    `;
    
    // 创建模态框
    const modalHtml = `
        <div class="modal fade" id="referralDetailModal">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-info-circle me-2"></i>
                            转诊详情 (#${referral.id})
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${modalBody}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除可能存在的旧模态框
    const oldModal = document.getElementById('referralDetailModal');
    if (oldModal) {
        oldModal.remove();
    }
    
    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('referralDetailModal'));
    modal.show();
}

// 优化查看大图功能
function viewFullImage(imagePath) {
    // 确保路径正确
    const formattedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
    
    const modalHtml = `
        <div class="modal fade" id="fullImageModal">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-image me-2"></i>医疗图片
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${formattedPath}" class="img-fluid" alt="医疗图片" style="max-height: 80vh;">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <a href="${formattedPath}" download class="btn btn-primary">
                            <i class="fas fa-download me-1"></i>下载图片
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除可能存在的旧模态框
    const oldModal = document.getElementById('fullImageModal');
    if (oldModal) {
        oldModal.remove();
    }
    
    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('fullImageModal'));
    modal.show();
}

// 修改reviewReferral函数，支持四种状态
async function reviewReferral(referralId, status) {
    // 状态中文名称，用于确认对话框
    const statusNames = {
        'accepted': '已接诊',
        'rejected': '未接诊',
        'transferred': '已转院',
        'recovered': '已康复'
    };
    
    if (!confirm(`确定将此转诊申请标记为【${statusNames[status]}】吗？`)) {
        return;
    }
    
    showLoading();
    try {
        const response = await fetch(`/api/referrals/${referralId}/review`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status })
        });
        
        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.msg || '处理转诊申请失败');
        }
        
        showToast(`转诊申请已标记为${statusNames[status]}`, 'success');
        await loadReferrals();
    } catch (error) {
        console.error('Error reviewing referral:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('token');
    currentUser = null;
    showLoginForm();
}

// 切换到注册表单
function switchToRegister() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    if (loginForm) loginForm.classList.add('hidden');
    if (registerForm) registerForm.classList.remove('hidden');
}

// 切换到登录表单
function switchToLogin() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    if (loginForm) loginForm.classList.remove('hidden');
    if (registerForm) registerForm.classList.add('hidden');
}

// 处理注册
async function handleRegister(event) {
    event.preventDefault();
    
    try {
        const form = event.target;
        if (!validateForm(form)) return;
        
        const formData = {
            username: form.username.value,
            password: form.password.value,
            fullName: form.fullName.value,
            phone: form.phone.value,
            town: form.town.value
        };
        
        const response = await fetch('/api/auth/register', {  // 修改为正确的API路径
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.msg || '注册失败');
        }
        
        showToast('注册成功', 'success');
        switchToLogin();  // 注册成功后跳转到登录界面
    } catch (error) {
        console.error('Register error:', error);
        showToast(error.message, 'danger');
    }
}

// 显示审核按钮
function showReviewButtons(referral) {
    // 只有上级医院且转诊状态为待处理时才显示审核按钮
    if (currentUser && currentUser.role === 'hospital' && referral.status === 'pending') {
        return `
            <button class="btn btn-sm btn-success" onclick="reviewReferral(${referral.id}, 'accepted')">
                <i class="fas fa-check"></i> 接收
            </button>
            <button class="btn btn-sm btn-danger" onclick="reviewReferral(${referral.id}, 'rejected')">
                <i class="fas fa-times"></i> 拒绝
            </button>
        `;
    }
    return '';
}

// 初始化应用
async function initApp() {
    initLoadingIndicator();
    
    // 检查令牌
    if (localStorage.getItem('token')) {
        try {
            const user = await verifyToken();
            if (user) {
                console.log("验证用户成功:", user);
                currentUser = user;
                showMainContent();
                await loadReferrals();
                
                // 初始化管理员面板
                initAdminPanel();
            } else {
                showLoginForm();
            }
        } catch (error) {
            console.error('Token verification error:', error);
            showLoginForm();
        }
    } else {
        showLoginForm();
    }
}

// 页面加载时初始化应用
document.addEventListener('DOMContentLoaded', initApp);

// 添加回 getUrgencyClass 函数
function getUrgencyClass(urgency) {
    switch (urgency) {
        case '高':
            return 'bg-danger';
        case '中':
            return 'bg-warning';
        case '低':
            return 'bg-success';
        default:
            return 'bg-secondary';
    }
}

// 获取状态卡片样式类
function getStatusCardClass(status) {
    switch (status) {
        case 'pending':
            return 'secondary';
        case 'accepted':
            return 'success';
        case 'rejected':
            return 'danger';
        default:
            return 'secondary';
    }
}

// 添加删除转诊功能
async function deleteReferral(referralId) {
    if (!confirm('确定要删除这条转诊记录吗？此操作不可恢复。')) {
        return;
    }

    showLoading();
    try {
        const response = await fetch(`/api/referrals/${referralId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Accept': 'application/json'
            }
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.msg || '删除失败');
        }

        showToast('转诊记录已删除', 'success');
        // 重新加载转诊列表
        await loadReferrals();
    } catch (error) {
        console.error('Error deleting referral:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 添加编辑备注功能
async function editNotes(referralId, currentNotes) {
    const notes = await new Promise(resolve => {
        const modalHtml = `
            <div class="modal fade" id="notesModal">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>
                                编辑备注
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <textarea id="notesInput" class="form-control" rows="3"
                                    placeholder="请输入备注">${currentNotes}</textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="submitNotes()">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除可能存在的旧模态框
        const oldModal = document.getElementById('notesModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('notesModal'));

        // 定义提交函数
        window.submitNotes = () => {
            const notes = document.getElementById('notesInput').value.trim();
            modal.hide();
            resolve(notes);
        };

        // 显示模态框
        modal.show();
    });

    if (notes === null || notes === currentNotes) return;

    showLoading();
    try {
        const response = await fetch(`/api/referrals/${referralId}/notes`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                notes: notes
            })
        });

        if (!response.ok) {
            throw new Error('备注更新失败');
        }

        showToast('备注更新成功', 'success');
        await loadReferrals();
    } catch (error) {
        console.error('Error updating notes:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 初始化管理员面板
function initAdminPanel() {
    console.log("当前用户角色:", currentUser?.role); // 添加调试日志
    if (currentUser && currentUser.role === 'admin') {
        const adminPanel = document.getElementById('adminPanel');
        if (adminPanel) {
            adminPanel.classList.remove('d-none');
            loadUsers();
        }
    } else {
        // 确保非管理员用户看不到管理面板
        const adminPanel = document.getElementById('adminPanel');
        if (adminPanel) {
            adminPanel.classList.add('d-none');
        }
    }
}

// 加载用户列表
async function loadUsers() {
    console.log("开始加载用户列表...");
    showLoading();
    try {
        const response = await fetch('/api/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            console.error("获取用户列表API返回错误:", response.status, response.statusText);
            throw new Error('获取用户列表失败');
        }

        const data = await response.json();
        console.log("获取到用户数据:", data);
        
        if (data.users) {
            displayUsers(data.users);
        } else {
            console.error("API返回数据中没有users字段:", data);
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 显示用户列表
function displayUsers(users) {
    console.log("显示用户列表, 用户数量:", users.length);
    const tbody = document.getElementById('userTableBody');
    if (tbody) {
        tbody.innerHTML = '';
        
        users.forEach(user => {
            const row = document.createElement('tr');
            
            // 角色显示名称转换
            const roleDisplay = {
                'admin': '系统管理员',
                'hospital': '上级医院',
                'town': '基层医疗机构'
            };
            
            // 创建操作按钮
            const operationButtons = `
                <button class="btn btn-sm btn-primary" onclick="showEditUserModal(${user.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                ${user.username !== currentUser.username ? `
                    <button class="btn btn-sm btn-danger ms-2" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                ` : ''}
            `;
            
            row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.fullName}</td>
                <td>${user.hospital_name}</td>
                <td>${roleDisplay[user.role] || user.role}</td>
                <td>${user.phone}</td>
                <td>${user.created_at}</td>
                <td>${operationButtons}</td>
            `;
            
            tbody.appendChild(row);
        });
    } else {
        console.error("找不到userTableBody元素");
    }
}

// 显示创建用户模态框
function showCreateUserModal() {
    const form = document.getElementById('createUserForm');
    if (form) {
        form.reset();
    }
    
    const modal = new bootstrap.Modal(document.getElementById('createUserModal'));
    modal.show();
}

// 处理创建用户
async function handleCreateUser(event) {
    event.preventDefault();
    const form = event.target;
    
    const userData = {
        username: form.username.value.trim(),
        password: form.password.value,
        fullName: form.fullName.value.trim(),
        hospital_name: form.hospital_name.value.trim(),
        phone: form.phone.value.trim(),
        role: form.role.value
    };
    
    showLoading();
    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.msg || '创建用户失败');
        }
        
        showToast('用户创建成功', 'success');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createUserModal'));
        modal.hide();
        
        // 重新加载用户列表
        await loadUsers();
    } catch (error) {
        console.error('Error creating user:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 显示编辑用户模态框
async function showEditUserModal(userId) {
    showLoading();
    try {
        // 获取用户详情
        const users = await fetchUsers();
        const user = users.find(u => u.id === userId);
        
        if (!user) {
            throw new Error('找不到该用户');
        }
        
        // 填充表单
        document.getElementById('editUserId').value = user.id;
        document.getElementById('editUsername').value = user.username;
        document.getElementById('editFullName').value = user.fullName;
        document.getElementById('editHospitalName').value = user.hospital_name;
        document.getElementById('editPhone').value = user.phone;
        document.getElementById('editRole').value = user.role;
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        modal.show();
    } catch (error) {
        console.error('Error showing edit modal:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 获取所有用户
async function fetchUsers() {
    try {
        const response = await fetch('/api/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Accept': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('获取用户列表失败');
        }
        
        const data = await response.json();
        return data.users || [];
    } catch (error) {
        console.error('Error fetching users:', error);
        return [];
    }
}

// 处理更新用户
async function handleUpdateUser(event) {
    event.preventDefault();
    const form = event.target;
    const userId = form.userId.value;
    
    const userData = {
        fullName: form.fullName.value.trim(),
        hospital_name: form.hospital_name.value.trim(),
        phone: form.phone.value.trim(),
        role: form.role.value
    };
    
    // 只有当密码字段有值时才添加密码
    if (form.password.value.trim()) {
        userData.password = form.password.value;
    }
    
    showLoading();
    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.msg || '更新用户失败');
        }
        
        showToast('用户更新成功', 'success');
        await loadUsers();
    } catch (error) {
        console.error('Error updating user:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 验证token并获取用户信息
async function verifyToken() {
    try {
        const response = await fetch('/api/auth/verify', {  // 修改为正确的API路径
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Accept': 'application/json'
            }
        });
        
        if (!response.ok) {
            localStorage.removeItem('token');
            throw new Error('Token invalid');
        }
        
        const data = await response.json();
        console.log("验证Token返回数据:", data);
        if (data.user) {
            // 检查用户角色
            if (data.user.role === 'admin') {
                console.log("当前用户是管理员");
            } else {
                console.log("当前用户不是管理员, 角色是:", data.user.role);
            }
            return data.user;
        }
        return null;
    } catch (error) {
        console.error('Token verification error:', error);
        localStorage.removeItem('token');
        throw error;
    }
}

// 添加删除用户功能 (之前缺少的函数)
async function deleteUser(userId) {
    if (!confirm('确定要删除该用户吗？此操作不可恢复。')) {
        return;
    }
    
    showLoading();
    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Accept': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.msg || '删除用户失败');
        }
        
        showToast('用户已删除', 'success');
        
        // 重新加载用户列表
        await loadUsers();
    } catch (error) {
        console.error('Error deleting user:', error);
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}