from app import create_app, db
import pymysql

def init_databases():
    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host='*************',
            user='root',
            password='windows1',
            port=3306
        )
        cursor = connection.cursor()
        
        # 创建数据库
        try:
            # 删除现有数据库（如果存在）
            cursor.execute("DROP DATABASE IF EXISTS referral_system")
            cursor.execute("DROP DATABASE IF EXISTS user_system")
            
            # 创建新数据库
            cursor.execute("CREATE DATABASE referral_system")
            cursor.execute("CREATE DATABASE user_system")
            connection.commit()
            print("Databases created successfully")
        except Exception as e:
            print(f"Error creating databases: {e}")
            raise
        finally:
            cursor.close()
            connection.close()

        # 创建应用上下文
        app = create_app('development')
        with app.app_context():
            # 创建所有表
            db.create_all()
            print("Tables created successfully")

    except Exception as e:
        print(f"Database initialization error: {e}")
        raise

if __name__ == '__main__':
    init_databases() 