* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    --main-color: #1a6aa1;
    --secondary-color: #4ba3c3;
}

body {
    background: #f8f9fa;
    font-family: '微软雅黑', 'Helvetica Neue', sans-serif;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.form-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

input, select, textarea {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 10px 20px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #0056b3;
}

.hidden {
    display: none;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: #fff;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
}

.referral-list {
    margin-top: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.switch-form {
    margin-top: 15px;
    text-align: center;
}

.switch-form a {
    color: #007bff;
    text-decoration: none;
}

.switch-form a:hover {
    text-decoration: underline;
}

.navbar {
    background: linear-gradient(135deg, var(--main-color), var(--secondary-color));
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(25, 113, 194, 0.25);
}

.status-badge {
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
}

.urgent { 
    background: #ff4757; 
    color: white; 
}

.high { 
    background: #ffa502; 
    color: white; 
}

.medium { 
    background: #2ed573; 
    color: white; 
}

.table th {
    background-color: #f8f9fa;
}

.btn-primary {
    background-color: var(--main-color);
    border-color: var(--main-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.modal-content {
    border-radius: 15px;
}

.modal-header {
    background: linear-gradient(135deg, var(--main-color), var(--secondary-color));
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    color: white;
}

/* 加载指示器样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.3s ease;
}

.loading-overlay.hidden {
    display: none;
    opacity: 0;
}

.spinner-container {
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 提示框样式 */
.toast {
    opacity: 1 !important;
}

/* 密码强度指示器 */
.password-strength {
    height: 5px;
    transition: all 0.3s ease;
}

.strength-weak {
    background: #ff4757;
    width: 30%;
}

.strength-medium {
    background: #ffa502;
    width: 60%;
}

.strength-strong {
    background: #2ed573;
    width: 100%;
}

/* 表单验证样式 */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,...");
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,...");
}

.notes-text {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.notes-text:hover {
    white-space: normal;
    overflow: visible;
}

/* 添加移动端优化样式 */
@media (max-width: 768px) {
    /* 调整表格显示 */
    .table-responsive {
        margin: 0;
        padding: 0;
    }
    
    /* 增大按钮点击区域 */
    .btn {
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        min-height: 44px; /* 确保足够的触摸区域 */
    }
    
    /* 调整按钮组布局 */
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    /* 调整模态框在移动端的显示 */
    .modal-dialog {
        margin: 0.5rem;
        max-width: 100%;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    /* 优化表单元素在移动端的显示 */
    .form-control, .form-select {
        height: 44px; /* 更容易点击 */
        font-size: 16px; /* 防止iOS自动缩放 */
    }
    
    /* 调整表格内容在移动端的显示 */
    .table td {
        white-space: normal; /* 允许文本换行 */
        min-width: 100px; /* 确保最小宽度 */
    }
    
    /* 优化备注显示 */
    .notes-preview {
        max-width: 150px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* 调整卡片样式 */
    .card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* 调整导航栏在移动端的显示 */
    .navbar {
        padding: 0.5rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* 添加触摸反馈效果 */
.btn:active {
    transform: scale(0.98);
}

/* 优化图片查看体验 */
.img-thumbnail {
    max-width: 100%;
    height: auto;
}

/* 医院标识样式 */
.hospital-badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.hospital-badge:hover {
    transform: scale(1.05);
}

/* 不同医院的颜色主题 */
.hospital-maternity {
    background-color: #e91e63;
    color: white;
}

.hospital-peoples {
    background-color: #2196f3;
    color: white;
}

.hospital-tcm {
    background-color: #4caf50;
    color: white;
}

/* 医院选择下拉框样式 */
#hospitalSelection {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 2px dashed #dee2e6;
}

#hospitalSelection label {
    font-weight: 600;
    color: #495057;
}

#hospitalSelection select {
    border: 2px solid #dee2e6;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#hospitalSelection select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

/* 可以添加自定义样式来调整医院名称的显示 */
.hospital-name {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 2rem;
}