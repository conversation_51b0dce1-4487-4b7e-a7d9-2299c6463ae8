#!/bin/bash

# 修复502 Bad Gateway错误的脚本

set -e

DOMAIN="referral.beimoyinhenlinlin.cn"
APP_DIR="/var/www/referral"
SERVICE_NAME="referral-system"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    print_error "请以root权限运行此脚本"
    exit 1
fi

print_status "🔍 开始诊断502 Bad Gateway错误..."

# 1. 检查应用目录是否存在
print_info "=== 检查应用目录 ==="
if [ -d "$APP_DIR" ]; then
    print_status "✅ 应用目录存在: $APP_DIR"
    ls -la "$APP_DIR" | head -10
else
    print_error "❌ 应用目录不存在: $APP_DIR"
    echo "需要重新部署应用"
    exit 1
fi
echo ""

# 2. 检查Python虚拟环境
print_info "=== 检查Python虚拟环境 ==="
if [ -d "$APP_DIR/venv" ]; then
    print_status "✅ 虚拟环境存在"
    if [ -x "$APP_DIR/venv/bin/python" ]; then
        print_status "✅ Python可执行"
        sudo -u referral "$APP_DIR/venv/bin/python" --version
    else
        print_error "❌ Python不可执行"
    fi
else
    print_error "❌ 虚拟环境不存在"
    echo "需要重新创建虚拟环境"
fi
echo ""

# 3. 检查应用文件
print_info "=== 检查应用文件 ==="
if [ -f "$APP_DIR/app.py" ]; then
    print_status "✅ app.py存在"
else
    print_error "❌ app.py不存在"
fi

if [ -f "$APP_DIR/requirements.txt" ]; then
    print_status "✅ requirements.txt存在"
else
    print_error "❌ requirements.txt不存在"
fi
echo ""

# 4. 检查Supervisor配置和状态
print_info "=== 检查Supervisor状态 ==="
if supervisorctl status $SERVICE_NAME; then
    print_info "Supervisor状态已显示"
else
    print_error "❌ Supervisor状态异常"
fi
echo ""

# 5. 检查端口监听
print_info "=== 检查端口监听 ==="
if netstat -tlnp | grep ":5000"; then
    print_status "✅ 端口5000有进程监听"
else
    print_error "❌ 端口5000无进程监听"
fi
echo ""

# 6. 检查应用日志
print_info "=== 检查应用日志 ==="
if [ -f "$APP_DIR/logs/app.log" ]; then
    print_status "最近的应用日志:"
    tail -20 "$APP_DIR/logs/app.log"
else
    print_warning "⚠️  应用日志文件不存在"
fi
echo ""

# 7. 检查Nginx错误日志
print_info "=== 检查Nginx错误日志 ==="
if [ -f "/var/log/nginx/error.log" ]; then
    print_status "最近的Nginx错误日志:"
    tail -10 /var/log/nginx/error.log
else
    print_warning "⚠️  Nginx错误日志文件不存在"
fi
echo ""

# 8. 尝试手动启动应用进行测试
print_info "=== 尝试手动测试应用 ==="
cd "$APP_DIR"
print_status "切换到应用目录: $APP_DIR"

# 检查环境变量文件
if [ -f ".env" ]; then
    print_status "✅ .env文件存在"
else
    print_warning "⚠️  .env文件不存在，创建默认配置..."
    cat > .env << EOF
FLASK_ENV=production
DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/referral_system
USER_DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/user_system
JWT_SECRET_KEY=referral-system-jwt-secret-2024
EOF
    chown referral:referral .env
    chmod 600 .env
fi

# 测试Python依赖
print_status "测试Python依赖..."
if sudo -u referral venv/bin/pip list | grep -q Flask; then
    print_status "✅ Flask已安装"
else
    print_error "❌ Flask未安装，重新安装依赖..."
    sudo -u referral venv/bin/pip install -r requirements.txt
fi

# 9. 修复常见问题
print_info "=== 修复常见问题 ==="

# 停止现有服务
print_status "停止现有服务..."
supervisorctl stop $SERVICE_NAME || true

# 确保目录权限正确
print_status "修复目录权限..."
chown -R referral:referral "$APP_DIR"
chmod -R 755 "$APP_DIR"
chmod 600 "$APP_DIR/.env" 2>/dev/null || true

# 确保日志目录存在
mkdir -p "$APP_DIR/logs"
chown referral:referral "$APP_DIR/logs"

# 重新创建Supervisor配置
print_status "重新创建Supervisor配置..."
cat > /etc/supervisor/conf.d/$SERVICE_NAME.conf << EOF
[program:$SERVICE_NAME]
command=$APP_DIR/venv/bin/python app.py
directory=$APP_DIR
user=referral
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$APP_DIR/logs/app.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=FLASK_ENV=production
EOF

# 重新加载Supervisor配置
print_status "重新加载Supervisor配置..."
supervisorctl reread
supervisorctl update

# 启动应用
print_status "启动应用..."
supervisorctl start $SERVICE_NAME

# 等待应用启动
print_status "等待应用启动..."
sleep 10

# 10. 验证修复结果
print_info "=== 验证修复结果 ==="

# 检查应用状态
if supervisorctl status $SERVICE_NAME | grep -q "RUNNING"; then
    print_status "✅ 应用正在运行"
else
    print_error "❌ 应用启动失败"
    print_status "查看详细日志:"
    supervisorctl status $SERVICE_NAME
    if [ -f "$APP_DIR/logs/app.log" ]; then
        tail -20 "$APP_DIR/logs/app.log"
    fi
fi

# 检查端口监听
if netstat -tlnp | grep -q ":5000"; then
    print_status "✅ 端口5000正在监听"
else
    print_error "❌ 端口5000未监听"
fi

# 测试本地连接
print_status "测试本地连接..."
LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5000/health 2>/dev/null || echo "000")
if [ "$LOCAL_STATUS" = "200" ]; then
    print_status "✅ 本地连接正常"
else
    print_error "❌ 本地连接失败，状态码: $LOCAL_STATUS"
fi

# 测试通过域名访问
print_status "测试域名访问..."
DOMAIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/health 2>/dev/null || echo "000")
if [ "$DOMAIN_STATUS" = "200" ]; then
    print_status "✅ 域名访问正常"
    echo ""
    print_status "🎉 502错误已修复！"
    echo "可以访问: https://$DOMAIN"
elif [ "$DOMAIN_STATUS" = "502" ]; then
    print_error "❌ 仍然是502错误"
    echo ""
    print_status "需要进一步诊断，请查看日志:"
    echo "  应用日志: tail -f $APP_DIR/logs/app.log"
    echo "  Nginx日志: tail -f /var/log/nginx/error.log"
else
    print_warning "⚠️  域名访问状态码: $DOMAIN_STATUS"
fi

echo ""
print_status "诊断完成。"
echo ""
print_status "如果问题仍然存在，请运行以下命令查看实时日志:"
echo "  sudo tail -f $APP_DIR/logs/app.log"
echo "  sudo tail -f /var/log/nginx/error.log"
echo ""
print_status "或者尝试手动启动应用进行调试:"
echo "  cd $APP_DIR"
echo "  sudo -u referral venv/bin/python app.py"
