#!/bin/bash

# 修复Nginx SSL配置脚本

set -e

DOMAIN="referral.beimoyinhenlinlin.cn"
APP_DIR="/var/www/referral"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    print_error "请以root权限运行此脚本"
    exit 1
fi

print_status "开始修复Nginx SSL配置..."

# 1. 检查SSL证书是否存在
print_status "检查SSL证书..."
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ] && [ -f "/etc/letsencrypt/live/$DOMAIN/privkey.pem" ]; then
    print_status "✅ SSL证书文件存在"
else
    print_error "❌ SSL证书文件不存在"
    echo "请先运行: certbot --nginx -d $DOMAIN"
    exit 1
fi

# 2. 备份现有配置
print_status "备份现有Nginx配置..."
if [ -f "/etc/nginx/sites-available/$DOMAIN" ]; then
    cp "/etc/nginx/sites-available/$DOMAIN" "/etc/nginx/sites-available/$DOMAIN.backup.$(date +%Y%m%d_%H%M%S)"
    print_status "配置已备份"
fi

# 3. 创建完整的Nginx配置（包含SSL）
print_status "创建新的Nginx配置..."
cat > "/etc/nginx/sites-available/$DOMAIN" << 'EOF'
server {
    listen 80;
    server_name referral.beimoyinhenlinlin.cn;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name referral.beimoyinhenlinlin.cn;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/referral.beimoyinhenlinlin.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/referral.beimoyinhenlinlin.cn/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 静态文件
    location /static/ {
        alias /var/www/referral/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads/ {
        alias /var/www/referral/static/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        access_log off;
    }
    
    # 测试端点
    location /test {
        proxy_pass http://127.0.0.1:5000/test;
        access_log off;
    }
}
EOF

print_status "Nginx配置文件已创建"

# 4. 启用站点配置
print_status "启用站点配置..."
ln -sf "/etc/nginx/sites-available/$DOMAIN" "/etc/nginx/sites-enabled/$DOMAIN"

# 删除默认站点（如果存在）
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    rm -f "/etc/nginx/sites-enabled/default"
    print_status "已删除默认站点配置"
fi

# 5. 测试Nginx配置
print_status "测试Nginx配置..."
if nginx -t; then
    print_status "✅ Nginx配置测试通过"
else
    print_error "❌ Nginx配置测试失败"
    exit 1
fi

# 6. 重启Nginx
print_status "重启Nginx..."
systemctl restart nginx

if systemctl is-active --quiet nginx; then
    print_status "✅ Nginx重启成功"
else
    print_error "❌ Nginx重启失败"
    systemctl status nginx
    exit 1
fi

# 7. 等待服务稳定
print_status "等待服务稳定..."
sleep 5

# 8. 测试HTTP和HTTPS访问
print_status "测试访问..."

# 测试HTTP（应该重定向到HTTPS）
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN/health)
print_status "HTTP状态码: $HTTP_STATUS"

# 测试HTTPS
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/health)
print_status "HTTPS状态码: $HTTPS_STATUS"

if [ "$HTTPS_STATUS" = "200" ]; then
    print_status "✅ HTTPS访问成功！"
else
    print_warning "⚠️  HTTPS访问可能需要等待几分钟"
fi

# 9. 显示最终结果
echo ""
print_status "🎉 SSL配置修复完成！"
echo ""
echo "访问地址:"
echo "  HTTP:  http://$DOMAIN (会自动重定向到HTTPS)"
echo "  HTTPS: https://$DOMAIN"
echo ""
echo "健康检查:"
echo "  HTTPS: https://$DOMAIN/health"
echo ""

# 10. 验证SSL证书
print_status "验证SSL证书..."
if command -v openssl >/dev/null 2>&1; then
    echo "SSL证书信息:"
    echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject -dates
fi

echo ""
print_status "默认医院管理员账户:"
echo "  涟源市妇幼保健院: maternity_admin / 123456"
echo "  涟源市人民医院: peoples_admin / 123456"
echo "  涟源市中医院: tcm_admin / 123456"
echo ""
print_warning "⚠️  请立即修改默认密码！"

print_status "配置完成！现在可以安全地访问 https://$DOMAIN"
