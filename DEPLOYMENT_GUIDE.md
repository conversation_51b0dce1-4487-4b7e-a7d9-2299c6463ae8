# 转诊系统生产环境部署指南

## 服务器信息
- **服务器IP**: *************
- **域名**: https://referral.beimoyinhenlinlin.cn
- **数据库**: MySQL (*************:3306)
- **数据库用户**: root / windows1

## 部署步骤

### 1. 准备服务器环境

首先确保您有服务器的SSH访问权限：

```bash
ssh root@*************
```

### 2. 上传代码到服务器

将项目文件上传到服务器：

```bash
# 方法1: 使用scp
scp -r . root@*************:/tmp/referral-system/

# 方法2: 使用rsync
rsync -avz --exclude='.git' --exclude='__pycache__' . root@*************:/tmp/referral-system/
```

### 3. 在服务器上运行部署脚本

```bash
# 登录服务器
ssh root@*************

# 进入项目目录
cd /tmp/referral-system

# 给部署脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

### 4. 配置域名DNS

确保域名 `referral.beimoyinhenlinlin.cn` 的A记录指向服务器IP `*************`。

### 5. 验证部署

部署完成后，访问以下地址验证：

- **主页**: https://referral.beimoyinhenlinlin.cn
- **健康检查**: https://referral.beimoyinhenlinlin.cn/health
- **测试端点**: https://referral.beimoyinhenlinlin.cn/test

## 部署后管理

### 使用管理脚本

```bash
# 给管理脚本执行权限
chmod +x /var/www/referral/manage.sh

# 创建软链接方便使用
sudo ln -sf /var/www/referral/manage.sh /usr/local/bin/referral

# 查看应用状态
referral status

# 查看应用日志
referral logs

# 重启应用
referral restart
```

### 常用管理命令

```bash
# 查看应用状态
sudo supervisorctl status referral-system

# 重启应用
sudo supervisorctl restart referral-system

# 查看应用日志
sudo tail -f /var/www/referral/logs/app.log

# 重启Nginx
sudo systemctl restart nginx

# 查看Nginx状态
sudo systemctl status nginx
```

## 默认账户信息

部署完成后，系统会自动创建以下医院管理员账户：

| 医院 | 用户名 | 密码 | 角色 |
|------|--------|------|------|
| 涟源市妇幼保健院 | maternity_admin | 123456 | hospital |
| 涟源市人民医院 | peoples_admin | 123456 | hospital |
| 涟源市中医院 | tcm_admin | 123456 | hospital |

**⚠️ 重要提醒**: 部署完成后请立即修改这些默认密码！

## 安全配置

### 1. 修改默认密码

登录系统后，立即修改所有默认账户的密码。

### 2. 防火墙配置

```bash
# 安装UFW防火墙
sudo apt install ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

### 3. SSL证书自动更新

```bash
# 添加证书自动更新任务
sudo crontab -e

# 添加以下行（每月1号凌晨2点更新证书）
0 2 1 * * /usr/bin/certbot renew --nginx --quiet && /bin/systemctl reload nginx
```

## 监控和维护

### 1. 日志监控

```bash
# 应用日志
sudo tail -f /var/www/referral/logs/app.log

# Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -f -u referral-system
```

### 2. 性能监控

```bash
# 查看系统资源使用
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tlnp
```

### 3. 数据库备份

```bash
# 手动备份
referral backup

# 设置自动备份（每天凌晨3点）
sudo crontab -e

# 添加以下行
0 3 * * * /var/www/referral/manage.sh backup
```

## 故障排除

### 1. 应用无法启动

```bash
# 查看应用状态
sudo supervisorctl status referral-system

# 查看应用日志
sudo tail -f /var/www/referral/logs/app.log

# 手动启动应用测试
cd /var/www/referral
sudo -u referral ./venv/bin/python app.py
```

### 2. 数据库连接问题

```bash
# 测试数据库连接
mysql -h ************* -u root -p

# 检查数据库是否存在
SHOW DATABASES;
USE referral_system;
SHOW TABLES;
```

### 3. SSL证书问题

```bash
# 检查证书状态
sudo certbot certificates

# 手动更新证书
sudo certbot renew --nginx

# 测试Nginx配置
sudo nginx -t
```

### 4. 权限问题

```bash
# 修复文件权限
sudo chown -R referral:referral /var/www/referral
sudo chmod -R 755 /var/www/referral
sudo chmod 600 /var/www/referral/.env
```

## 更新部署

### 1. 更新应用代码

```bash
# 使用管理脚本更新
referral update

# 或手动更新
sudo supervisorctl stop referral-system
cd /var/www/referral
sudo -u referral git pull origin main
sudo -u referral ./venv/bin/pip install -r requirements.txt
sudo supervisorctl start referral-system
```

### 2. 数据库迁移

```bash
# 运行数据库迁移
referral migrate
```

## 性能优化

### 1. Nginx优化

编辑 `/etc/nginx/sites-available/referral.beimoyinhenlinlin.cn`：

```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 设置缓存
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 2. 数据库优化

```sql
-- 添加索引优化查询性能
USE referral_system;
CREATE INDEX idx_referral_status ON referral(status);
CREATE INDEX idx_referral_hospital ON referral(to_hospital);
CREATE INDEX idx_referral_created ON referral(created_at);
```

## 联系信息

如果在部署过程中遇到问题，请检查：

1. 服务器网络连接
2. 域名DNS配置
3. 数据库连接
4. 防火墙设置
5. SSL证书状态

部署完成后，系统将在 https://referral.beimoyinhenlinlin.cn 上运行。
