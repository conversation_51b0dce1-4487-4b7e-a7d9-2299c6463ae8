#!/bin/bash

# 转诊系统管理脚本

APP_DIR="/var/www/referral"
SERVICE_NAME="referral-system"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "转诊系统管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start       启动应用"
    echo "  stop        停止应用"
    echo "  restart     重启应用"
    echo "  status      查看应用状态"
    echo "  logs        查看应用日志"
    echo "  nginx       重启Nginx"
    echo "  ssl         更新SSL证书"
    echo "  backup      备份数据库"
    echo "  update      更新应用代码"
    echo "  migrate     运行数据库迁移"
    echo "  help        显示此帮助信息"
}

# 启动应用
start_app() {
    print_status "启动应用..."
    sudo supervisorctl start $SERVICE_NAME
    sudo systemctl start nginx
    print_status "应用启动完成"
}

# 停止应用
stop_app() {
    print_status "停止应用..."
    sudo supervisorctl stop $SERVICE_NAME
    print_status "应用停止完成"
}

# 重启应用
restart_app() {
    print_status "重启应用..."
    sudo supervisorctl restart $SERVICE_NAME
    sudo systemctl reload nginx
    print_status "应用重启完成"
}

# 查看应用状态
show_status() {
    print_info "=== 应用状态 ==="
    sudo supervisorctl status $SERVICE_NAME
    
    print_info "=== Nginx状态 ==="
    sudo systemctl status nginx --no-pager -l
    
    print_info "=== 端口监听 ==="
    netstat -tlnp | grep -E ":(80|443|5000)"
    
    print_info "=== 磁盘使用 ==="
    df -h $APP_DIR
    
    print_info "=== 内存使用 ==="
    free -h
}

# 查看日志
show_logs() {
    echo "选择要查看的日志:"
    echo "1) 应用日志"
    echo "2) Nginx访问日志"
    echo "3) Nginx错误日志"
    echo "4) 系统日志"
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            print_info "应用日志 (按Ctrl+C退出):"
            sudo tail -f $APP_DIR/logs/app.log
            ;;
        2)
            print_info "Nginx访问日志 (按Ctrl+C退出):"
            sudo tail -f /var/log/nginx/access.log
            ;;
        3)
            print_info "Nginx错误日志 (按Ctrl+C退出):"
            sudo tail -f /var/log/nginx/error.log
            ;;
        4)
            print_info "系统日志 (按Ctrl+C退出):"
            sudo journalctl -f -u $SERVICE_NAME
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 重启Nginx
restart_nginx() {
    print_status "重启Nginx..."
    sudo nginx -t && sudo systemctl restart nginx
    print_status "Nginx重启完成"
}

# 更新SSL证书
update_ssl() {
    print_status "更新SSL证书..."
    sudo certbot renew --nginx
    sudo systemctl reload nginx
    print_status "SSL证书更新完成"
}

# 备份数据库
backup_database() {
    print_status "备份数据库..."
    
    BACKUP_DIR="$APP_DIR/backups"
    sudo mkdir -p $BACKUP_DIR
    
    DATE=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/referral_backup_$DATE.sql"
    
    # 备份转诊系统数据库
    mysqldump -h ************* -u root -pwindows1 referral_system > $BACKUP_FILE
    
    # 备份用户系统数据库
    mysqldump -h ************* -u root -pwindows1 user_system >> $BACKUP_FILE
    
    # 压缩备份文件
    gzip $BACKUP_FILE
    
    print_status "数据库备份完成: ${BACKUP_FILE}.gz"
    
    # 清理7天前的备份
    find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
    print_status "清理旧备份完成"
}

# 更新应用代码
update_app() {
    print_status "更新应用代码..."
    
    # 停止应用
    sudo supervisorctl stop $SERVICE_NAME
    
    # 备份当前代码
    sudo cp -r $APP_DIR $APP_DIR.backup.$(date +%Y%m%d_%H%M%S)
    
    # 这里假设代码在Git仓库中
    cd $APP_DIR
    sudo -u referral git pull origin main
    
    # 更新Python依赖
    sudo -u referral $APP_DIR/venv/bin/pip install -r requirements.txt
    
    # 重启应用
    sudo supervisorctl start $SERVICE_NAME
    
    print_status "应用代码更新完成"
}

# 运行数据库迁移
run_migration() {
    print_status "运行数据库迁移..."
    
    cd $APP_DIR
    sudo -u referral $APP_DIR/venv/bin/python migrate_hospitals.py
    
    print_status "数据库迁移完成"
}

# 主函数
main() {
    case "$1" in
        start)
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            restart_app
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        nginx)
            restart_nginx
            ;;
        ssl)
            update_ssl
            ;;
        backup)
            backup_database
            ;;
        update)
            update_app
            ;;
        migrate)
            run_migration
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@"
