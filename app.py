from flask import Flask, request, jsonify, render_template, redirect, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity, get_jwt
from datetime import datetime, timedelta
import logging
import pymysql
import re
from werkzeug.security import generate_password_hash
from config import config
import os
from werkzeug.security import check_password_hash
from flask_cors import CORS
from pathlib import Path
from werkzeug.utils import secure_filename
import json
from sqlalchemy import case

# 创建日志目录
def setup_logging():
    if os.name == 'nt':  # Windows 系统
        log_dir = Path('logs')
    else:  # Linux/Unix 系统
        log_dir = Path('/var/log/referral')
    
    # 创建日志目录（如果不存在）
    log_dir.mkdir(parents=True, exist_ok=True)

# 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'error.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# 初始化日志
logger = setup_logging()

# 初始化 JWTManager
jwt = JWTManager()

# 配置上传文件存储位置
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

# 创建应用工厂函数
def create_app(config_name='default'):
    app = Flask(__name__, 
                template_folder='templates',  # 明确指定模板目录
                static_folder='static')       # 明确指定静态文件目录
    
    # 添加 CORS 支持
    CORS(app, resources={
        r"/*": {
            "origins": ["http://127.0.0.1:5000", "http://localhost:5000"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "Accept"],
            "supports_credentials": True
        }
    })
    
    # 添加静态文件缓存控制
    @app.after_request
    def add_header(response):
        # 对所有静态文件（特别是JS文件）禁用缓存
        if request.path.startswith('/static/'):
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        return response
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 添加文件上传配置
    app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传大小为16MB
    
    # 确保上传目录存在
    os.makedirs(os.path.join(app.root_path, UPLOAD_FOLDER), exist_ok=True)
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    
    return app

# 初始化 SQLAlchemy
db = SQLAlchemy()

# 数据库模型定义
class Patient(db.Model):
    __tablename__ = 'patient'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), nullable=False)
    age = db.Column(db.Integer, nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    medical_history = db.Column(db.Text, nullable=True)
    current_condition = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    # 修改反向关系定义
    referrals = db.relationship('Referral', backref='patient', lazy=True)

class Referral(db.Model):
    __tablename__ = 'referral'
    
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patient.id', ondelete='CASCADE'), nullable=False)
    from_hospital = db.Column(db.String(120), nullable=False)
    to_hospital = db.Column(db.String(120), nullable=False)
    department_contact_name = db.Column(db.String(80))
    department_contact_phone = db.Column(db.String(20))
    doctor_name = db.Column(db.String(80), nullable=False)
    doctor_phone = db.Column(db.String(20), nullable=False)
    reason = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='pending')
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    reviewed_by = db.Column(db.String(80))
    review_notes = db.Column(db.Text)
    reviewed_at = db.Column(db.DateTime)
    notes = db.Column(db.Text, nullable=True)
    image_path = db.Column(db.String(255), nullable=True)

class User(db.Model):
    __bind_key__ = 'users'
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    hospital_name = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False)
    fullName = db.Column(db.String(80), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

# 创建应用实例
app = create_app(os.getenv('FLASK_ENV', 'production'))

# 路由定义
@app.route('/')
def index():
    return render_template('index.html')

# 检查文件扩展名是否允许
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/api/referrals', methods=['POST'])
@jwt_required()
def create_referral():
    try:
        username = get_jwt_identity()
        claims = get_jwt()
        
        # 检查是否基层医院或管理员
        if claims.get('role') not in ['town', 'admin']:
            return jsonify({
                "msg": "只有基层医院可以创建转诊申请"
            }), 403
        
        # 检查内容类型并相应处理
        content_type = request.headers.get('Content-Type', '')
        
        # 初始化变量
        image_path = None
        notes = ''
        to_hospital = '上级医院'
        
        if 'multipart/form-data' in content_type:
            # 处理表单数据
            patient_info = {
                'name': request.form.get('patientName', '').strip(),
                'age': request.form.get('patientAge', ''),
                'gender': request.form.get('patientGender', '女')
            }
            
            notes = request.form.get('notes', '').strip()
            
            # 处理文件上传
            if 'medicalImage' in request.files:
                file = request.files['medicalImage']
                if file and file.filename and allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                    safe_filename = f"{timestamp}_{filename}"
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
                    file.save(filepath)
                    image_path = f"uploads/{safe_filename}"
        else:
            # 处理JSON数据
            data = request.json
            if not data:
                return jsonify({"msg": "未接收到数据"}), 400
                
            patient_info = data.get('patient_info', {})
            if 'gender' not in patient_info:
                patient_info['gender'] = '女'
            notes = data.get('notes', '')
        
        # 验证必要字段
        if not patient_info:
            return jsonify({"msg": "缺少患者信息"}), 422
            
        patient_required_fields = ['name', 'age', 'gender']
        if not all(field in patient_info for field in patient_required_fields):
            missing_fields = [f for f in patient_required_fields if f not in patient_info]
            return jsonify({"msg": f"患者信息不完整: {', '.join(missing_fields)}"}), 422
        
        # 准备转诊数据
        doctor_info = {
            'name': claims.get('fullName', username),
            'phone': claims.get('phone', '未提供')
        }
        
        try:
            # 创建患者记录
            patient = Patient(
                name=patient_info['name'].strip(),
                age=int(patient_info['age']),
                gender=patient_info['gender']
            )
            db.session.add(patient)
            db.session.flush()  # 获取患者ID

            # 创建转诊记录
            referral = Referral(
                patient_id=patient.id,
                from_hospital=claims.get('hospital_name', '未指定'),
                to_hospital=to_hospital,
                doctor_name=doctor_info['name'],
                doctor_phone=doctor_info['phone'],
                status='pending',
                notes=notes,
                image_path=image_path,
                reason='暂无原因'
            )
            db.session.add(referral)
            db.session.commit()

            logger.info(f"Created referral: {referral.id} for patient: {patient.name}")
            return jsonify({
                "msg": "转诊申请提交成功",
                "referral_id": referral.id
            }), 201
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Database error: {str(e)}")
            return jsonify({"msg": f"数据库错误: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"Create referral error: {str(e)}")
        return jsonify({"msg": f"创建转诊申请失败: {str(e)}"}), 500

def get_patients():
    try:
        patients = Patient.query.all()
        return jsonify([{
            'id': p.id,
            'name': p.name,
            'age': p.age,
            'gender': p.gender
        } for p in patients])
    except Exception as e:
        logger.error(f"Error fetching patients: {str(e)}")
        return jsonify({"msg": "获取患者列表失败"}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.json
        if not data:
            return jsonify({"msg": "未接收到数据"}), 400

        username = data.get('username')
        password = data.get('password')
        remember = data.get('remember', False)

        if not username or not password:
            return jsonify({"msg": "用户名和密码不能为空"}), 400

        user = User.query.filter_by(username=username).first()
        
        # 修复：检查用户存在且添加异常处理
        if not user:
            return jsonify({"msg": "用户名或密码错误"}), 401
            
        # 检查密码
        try:
            is_valid = check_password_hash(user.password_hash, password)
            if not is_valid:
                return jsonify({"msg": "用户名或密码错误"}), 401
        except Exception as e:
            logger.error(f"Password check error: {str(e)}")
            return jsonify({"msg": "验证密码时出错"}), 500

        # 创建访问令牌，确保处理可能缺少的字段
        user_info = {
            'username': user.username,
            'role': user.role,
            'hospital_name': user.hospital_name,
            'fullName': getattr(user, 'fullName', username),
            'phone': getattr(user, 'phone', '')
        }
        
        # 设置令牌过期时间
        expires = timedelta(days=30) if remember else timedelta(hours=24)
        access_token = create_access_token(identity=username, additional_claims=user_info, expires_delta=expires)

        return jsonify({
            "access_token": access_token,
            "user": user_info
        })
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({"msg": "登录失败，请稍后重试"}), 500

@app.route('/api/referrals/<int:referral_id>/review', methods=['PUT'])
@jwt_required()
def review_referral(referral_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        username = get_jwt_identity()
        
        # 检查权限
        if role not in ['hospital', 'admin']:
            return jsonify({"msg": "无权限处理转诊申请"}), 403
        
        # 获取转诊记录
        referral = Referral.query.get(referral_id)
        if not referral:
            return jsonify({"msg": "转诊记录不存在"}), 404
            
        # 获取状态
        status = request.json.get('status')
        if not status or status not in ['accepted', 'rejected', 'transferred', 'recovered']:
            return jsonify({"msg": "无效的状态"}), 400
            
        # 更新转诊状态
        referral.status = status
        referral.reviewed_by = username
        referral.reviewed_at = datetime.now()
        
        # 保存更改
        db.session.commit()

        return jsonify({
            "msg": "转诊申请处理成功", 
            "status": status
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error reviewing referral: {str(e)}")
        return jsonify({"msg": "处理转诊申请失败"}), 500

@app.route('/api/referrals', methods=['GET'])
@jwt_required()
def get_referrals():
    try:
        # 获取用户信息
        username = get_jwt_identity()
        claims = get_jwt()
        role = claims.get('role')
        hospital_name = claims.get('hospital_name')
        
        # 根据角色筛选转诊申请
        query = Referral.query.join(Patient)
        
        if role == 'town':
            # 基层医疗机构只能看到自己创建的转诊
            query = query.filter(Referral.from_hospital == hospital_name)
        elif role == 'hospital':
            # 上级医院可以看到所有发送给"上级医院"的申请，或明确指定给自己医院的申请
            query = query.filter(
                (Referral.to_hospital == '上级医院') | 
                (Referral.to_hospital == hospital_name)
            )
        # 管理员可以看到所有转诊
        
        # 添加排序：先按状态（待处理优先），再按创建时间倒序
        referrals = query.order_by(
            case(
                (Referral.status == 'pending', 0),
                else_=1
            ),
            Referral.created_at.desc()
        ).all()
        
        # 构建响应数据
        result = []
        for referral in referrals:
            result.append({
                'id': referral.id,
                'from_hospital': referral.from_hospital,
                'to_hospital': referral.to_hospital,
                'doctor_name': referral.doctor_name,
                'doctor_phone': referral.doctor_phone,
                'status': referral.status,
                'created_at': referral.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'reviewed_at': referral.reviewed_at.strftime('%Y-%m-%d %H:%M:%S') if referral.reviewed_at else None,
                'reviewed_by': referral.reviewed_by,
                'review_notes': referral.review_notes,
                'notes': referral.notes,
                'image_path': referral.image_path,
                'reason': referral.reason,
                'patient': {
                    'id': referral.patient.id,
                    'name': referral.patient.name,
                    'age': referral.patient.age,
                    'gender': referral.patient.gender,
                    'medical_history': referral.patient.medical_history
                }
            })
        
        # 计算统计数据
        total = len(result)
        pending = sum(1 for r in result if r['status'] == 'pending')
        accepted = sum(1 for r in result if r['status'] == 'accepted')
        rejected = sum(1 for r in result if r['status'] == 'rejected')
        
        return jsonify({
            'referrals': result,
            'statistics': {
                'total': total,
                'pending': pending,
                'accepted': accepted,
                'rejected': rejected
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting referrals: {str(e)}")
        return jsonify({"msg": f"获取转诊列表失败: {str(e)}"}), 500

@app.route('/api/referrals/<int:referral_id>', methods=['GET'])
@jwt_required()
def get_referral_detail(referral_id):
    try:
        claims = get_jwt()
        referral = Referral.query.get_or_404(referral_id)
        patient = referral.patient

        if claims.get('role') == 'town' and referral.from_hospital != claims.get('hospital_name'):
            return jsonify({"msg": "无权查看此转诊记录"}), 403
        if claims.get('role') == 'hospital' and referral.to_hospital != claims.get('hospital_name'):
            return jsonify({"msg": "无权查看此转诊记录"}), 403

        return jsonify({
            "id": referral.id,
            "patient": {
                "name": patient.name,
                "age": patient.age,
                "gender": patient.gender,
                "medical_history": patient.medical_history,
                "current_condition": patient.current_condition
            },
            "from_hospital": referral.from_hospital,
            "to_hospital": referral.to_hospital,
            "department_contact_name": referral.department_contact_name,
            "doctor_name": referral.doctor_name,
            "doctor_phone": referral.doctor_phone,
            "reason": referral.reason,
            "status": referral.status,
            "created_at": referral.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "reviewed_by": referral.reviewed_by,
            "review_notes": referral.review_notes,
            "reviewed_at": referral.reviewed_at.strftime('%Y-%m-%d %H:%M:%S') if referral.reviewed_at else None
        })

    except Exception as e:
        logger.error(f"Error fetching referral detail: {str(e)}")
        return jsonify({"msg": "获取转诊详情失败"}), 500

@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.json
        
        # 基本字段验证
        required_fields = ['username', 'password', 'fullName', 'phone', 'town']
        if not all(field in data for field in required_fields):
            return jsonify({"msg": "请填写所有必填字段"}), 400
            
        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({"msg": "用户名已存在"}), 400
            
        # 创建新用户
        new_user = User(
            username=data['username'],
            password_hash=generate_password_hash(data['password']),
            fullName=data['fullName'],
            phone=data['phone'],
            hospital_name=data['town'],
            role='town'
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({"msg": "注册成功"}), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Register error: {str(e)}")
        return jsonify({"msg": f"注册失败: {str(e)}"}), 500

@app.route('/user-management')
@jwt_required()
def user_management():
    claims = get_jwt()
    if claims.get('role') != 'admin':
        return redirect('/')
    return render_template('user_management.html')

@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以访问用户列表"}), 403
            
        users = User.query.all()
        users_list = []
        
        for user in users:
            users_list.append({
                'id': user.id,
                'username': user.username,
                'fullName': getattr(user, 'fullName', user.username),
                'hospital_name': user.hospital_name,
                'role': user.role,
                'phone': getattr(user, 'phone', ''),
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else ''
            })
            
        return jsonify({"users": users_list})
        
    except Exception as e:
        logger.error(f"Get users error: {str(e)}")
        return jsonify({"msg": "获取用户列表失败"}), 500

@app.route('/api/users', methods=['POST'])
@jwt_required()
def create_user():
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以创建用户"}), 403
            
        data = request.json
        
        # 验证数据
        required_fields = ['username', 'password', 'hospital_name', 'role', 'fullName', 'phone']
        if not all(field in data for field in required_fields):
            missing_fields = [f for f in required_fields if f not in data]
            return jsonify({"msg": f"缺少必要字段: {', '.join(missing_fields)}"}), 400
            
        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({"msg": "用户名已存在"}), 400
            
        # 创建新用户
        new_user = User(
            username=data['username'],
            password_hash=generate_password_hash(data['password']),
            hospital_name=data['hospital_name'],
            role=data['role'],
            fullName=data['fullName'],
            phone=data['phone']
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            "msg": "用户创建成功",
            "user": {
                "id": new_user.id,
                "username": new_user.username,
                "hospital_name": new_user.hospital_name,
                "role": new_user.role
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Create user error: {str(e)}")
        return jsonify({"msg": f"创建用户失败: {str(e)}"}), 500

@app.route('/test')
def test():
    return jsonify({"status": "ok", "message": "Flask application is running"})

# 添加JWT错误处理
@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({
        'msg': '无效的访问令牌',
        'error': 'invalid_token'
    }), 401

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({
        'msg': '访问令牌已过期',
        'error': 'token_expired'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({
        'msg': '缺少访问令牌',
        'error': 'authorization_required'
    }), 401

# 添加新的路由用于验证 token 和获取用户信息
@app.route('/api/auth/verify', methods=['GET'])
@jwt_required()
def verify_token():
    try:
        claims = get_jwt()
        return jsonify({
            "valid": True,
            "user": {
                "username": claims.get('username'),
                "role": claims.get('role'),
                "hospital_name": claims.get('hospital_name')
            }
        })
    except Exception as e:
        app.logger.error(f"Token verification error: {str(e)}")
        return jsonify({"valid": False, "msg": "无效的令牌"}), 401

@app.route('/api/referrals/<int:referral_id>', methods=['DELETE'])
@jwt_required()
def delete_referral(referral_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        hospital_name = claims.get('hospital_name')

        # 获取转诊记录
        referral = Referral.query.get_or_404(referral_id)

        # 检查权限：只有创建者（基层医院）或管理员可以删除
        if role != 'admin' and (role != 'town' or referral.from_hospital != hospital_name):
            return jsonify({"msg": "无权删除此转诊记录"}), 403

        # 只能删除待处理的转诊
        if referral.status != 'pending':
            return jsonify({"msg": "只能删除待处理的转诊记录"}), 400

        # 删除关联的患者记录
        patient = Patient.query.get(referral.patient_id)
        if patient:
            db.session.delete(patient)

        # 删除转诊记录
        db.session.delete(referral)
        db.session.commit()

        return jsonify({"msg": "转诊记录已删除"}), 200

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting referral: {str(e)}")
        return jsonify({"msg": "删除转诊记录失败"}), 500

@app.context_processor
def inject_current_time():
    return {'current_time': int(datetime.now().timestamp())}

@app.route('/api/referrals/<int:referral_id>/notes', methods=['PUT'])
@jwt_required()
def update_referral_notes(referral_id):
    try:
        data = request.get_json()
        notes = data.get('notes', '').strip()
        
        referral = Referral.query.get_or_404(referral_id)
        referral.notes = notes
        db.session.commit()
        
        return jsonify({
            "msg": "备注更新成功",
            "notes": notes
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating referral notes: {str(e)}")
        return jsonify({"msg": "备注更新失败"}), 500

# 添加路由以访问上传的文件
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以更新用户信息"}), 403
            
        user = User.query.get_or_404(user_id)
        data = request.json
        
        # 更新用户信息
        if 'fullName' in data:
            user.fullName = data['fullName']
        if 'hospital_name' in data:
            user.hospital_name = data['hospital_name']
        if 'role' in data:
            user.role = data['role']
        if 'phone' in data:
            user.phone = data['phone']
        if 'password' in data and data['password'].strip():
            user.password_hash = generate_password_hash(data['password'])
        
        db.session.commit()
        
        return jsonify({
            "msg": "用户信息更新成功",
            "user": {
                "id": user.id,
                "username": user.username,
                "hospital_name": user.hospital_name,
                "role": user.role
            }
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Update user error: {str(e)}")
        return jsonify({"msg": f"更新用户信息失败: {str(e)}"}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以删除用户"}), 403
            
        user = User.query.get_or_404(user_id)
        
        # 不允许删除自己
        current_username = get_jwt_identity()
        if user.username == current_username:
            return jsonify({"msg": "不能删除当前登录的用户"}), 400
        
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({"msg": "用户已删除"})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Delete user error: {str(e)}")
        return jsonify({"msg": f"删除用户失败: {str(e)}"}), 500

# 在get_referrals函数中的状态映射
STATUS_MAP = {
    'pending': '待处理',
    'accepted': '已接诊',
    'rejected': '未接诊',
    'transferred': '已转院',
    'recovered': '已康复'
}

if __name__ == '__main__':
    with app.app_context():
        try:
            # 创建数据库连接
            connection = pymysql.connect(
                host='************',
                user='root', 
                password='windows1',
                port=3306
            )
            
            # 创建所有表
            db.create_all()
            print("Tables created successfully")
            
        except Exception as e:
            print(f"Database initialization error: {e}")
            
    # 开启调试模式以便查看详细错误
    app.run(host='0.0.0.0', port=5000, debug=True)
