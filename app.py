from flask import Flask, request, jsonify, render_template, redirect, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity, get_jwt
from datetime import datetime, timedelta, timezone
import logging
import pymysql
from werkzeug.security import generate_password_hash, check_password_hash
from config import config
import os
from flask_cors import CORS
from pathlib import Path
from werkzeug.utils import secure_filename
from sqlalchemy import case
from typing import Dict, List, Optional, Any

# 工具函数
def get_current_time() -> datetime:
    """获取当前UTC时间"""
    return datetime.now(timezone.utc)

def setup_logging() -> logging.Logger:
    """设置日志配置"""
    # 根据操作系统选择日志目录
    log_dir = Path('logs') if os.name == 'nt' else Path('/var/log/referral')

    # 创建日志目录（如果不存在）
    log_dir.mkdir(parents=True, exist_ok=True)

    # 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'error.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# 常量定义
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 医院配置
HOSPITALS = {
    'maternity': '涟源市妇幼保健院',
    'peoples': '涟源市人民医院',
    'tcm': '涟源市中医院'
}

# 上级医院列表（用于转诊选择）
UPPER_HOSPITALS = list(HOSPITALS.values())

# 状态映射
STATUS_MAP = {
    'pending': '待处理',
    'accepted': '已接诊',
    'rejected': '未接诊',
    'transferred': '已转院',
    'recovered': '已康复'
}

# 初始化日志
logger = setup_logging()

# 初始化 JWTManager
jwt = JWTManager()

# 工具函数
def allowed_file(filename: str) -> bool:
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
    """验证必需字段"""
    missing_fields = [field for field in required_fields if field not in data or not data[field]]
    return f"缺少必要字段: {', '.join(missing_fields)}" if missing_fields else None

# 创建应用工厂函数
def create_app(config_name: str = 'default') -> Flask:
    """创建Flask应用实例"""
    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')

    # 配置CORS
    allowed_origins = [
        "http://127.0.0.1:5000",
        "http://localhost:5000",
        "https://referral.beimoyinhenlinlin.cn",
        "http://referral.beimoyinhenlinlin.cn"
    ]

    CORS(app, resources={
        r"/*": {
            "origins": allowed_origins,
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "Accept"],
            "supports_credentials": True
        }
    })

    # 静态文件缓存控制
    @app.after_request
    def add_cache_headers(response):
        if request.path.startswith('/static/'):
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        return response

    # 加载配置
    app.config.from_object(config[config_name])

    # 文件上传配置
    app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
    app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

    # 确保上传目录存在
    os.makedirs(os.path.join(app.root_path, UPLOAD_FOLDER), exist_ok=True)

    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)

    return app

# 初始化 SQLAlchemy
db = SQLAlchemy()

# 数据库模型定义
class Patient(db.Model):
    __tablename__ = 'patient'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), nullable=False)
    age = db.Column(db.Integer, nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    medical_history = db.Column(db.Text, nullable=True)
    current_condition = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, default=get_current_time)
    # 修改反向关系定义
    referrals = db.relationship('Referral', backref='patient', lazy=True)

class Referral(db.Model):
    __tablename__ = 'referral'

    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patient.id', ondelete='CASCADE'), nullable=False)
    from_hospital = db.Column(db.String(120), nullable=False)
    to_hospital = db.Column(db.String(120), nullable=False)
    department_contact_name = db.Column(db.String(80))
    department_contact_phone = db.Column(db.String(20))
    doctor_name = db.Column(db.String(80), nullable=False)
    doctor_phone = db.Column(db.String(20), nullable=False)
    reason = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='pending')
    created_at = db.Column(db.DateTime, nullable=False, default=get_current_time)
    reviewed_by = db.Column(db.String(80))
    review_notes = db.Column(db.Text)
    reviewed_at = db.Column(db.DateTime)
    notes = db.Column(db.Text, nullable=True)
    image_path = db.Column(db.String(255), nullable=True)

class User(db.Model):
    __bind_key__ = 'users'
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    hospital_name = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False)
    fullName = db.Column(db.String(80), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, default=get_current_time)

# 创建应用实例
app = create_app(os.getenv('FLASK_ENV', 'production'))

# 路由定义
@app.route('/')
def index():
    return render_template('index.html')

def _process_form_data() -> tuple[Dict[str, Any], str, Optional[str], str]:
    """处理表单数据"""
    patient_info = {
        'name': request.form.get('patientName', '').strip(),
        'age': request.form.get('patientAge', ''),
        'gender': request.form.get('patientGender', '女')
    }

    notes = request.form.get('notes', '').strip()
    to_hospital = request.form.get('toHospital', UPPER_HOSPITALS[0])  # 默认第一个医院
    image_path = None

    # 验证目标医院是否有效
    if to_hospital not in UPPER_HOSPITALS:
        to_hospital = UPPER_HOSPITALS[0]

    # 处理文件上传
    if 'medicalImage' in request.files:
        file = request.files['medicalImage']
        if file and file.filename and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            safe_filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
            file.save(filepath)
            image_path = f"uploads/{safe_filename}"

    return patient_info, notes, image_path, to_hospital

def _process_json_data() -> tuple[Dict[str, Any], str, str]:
    """处理JSON数据"""
    data = request.json
    if not data:
        raise ValueError("未接收到数据")

    patient_info = data.get('patient_info', {})
    if 'gender' not in patient_info:
        patient_info['gender'] = '女'
    notes = data.get('notes', '')
    to_hospital = data.get('to_hospital', UPPER_HOSPITALS[0])  # 默认第一个医院

    # 验证目标医院是否有效
    if to_hospital not in UPPER_HOSPITALS:
        to_hospital = UPPER_HOSPITALS[0]

    return patient_info, notes, to_hospital

@app.route('/api/referrals', methods=['POST'])
@jwt_required()
def create_referral():
    try:
        claims = get_jwt()

        # 检查权限
        if claims.get('role') not in ['town', 'admin']:
            return jsonify({"msg": "只有基层医院可以创建转诊申请"}), 403

        # 根据内容类型处理数据
        content_type = request.headers.get('Content-Type', '')
        image_path = None
        to_hospital = UPPER_HOSPITALS[0]  # 默认值

        if 'multipart/form-data' in content_type:
            patient_info, notes, image_path, to_hospital = _process_form_data()
        else:
            patient_info, notes, to_hospital = _process_json_data()

        # 验证患者信息
        validation_error = validate_required_fields(patient_info, ['name', 'age', 'gender'])
        if validation_error:
            return jsonify({"msg": f"患者信息不完整: {validation_error}"}), 422
        
        # 准备转诊数据
        username = get_jwt_identity()
        doctor_info = {
            'name': claims.get('fullName', username),
            'phone': claims.get('phone', '未提供')
        }

        try:
            # 创建患者记录
            patient = Patient(
                name=patient_info['name'].strip(),
                age=int(patient_info['age']),
                gender=patient_info['gender']
            )
            db.session.add(patient)
            db.session.flush()  # 获取患者ID

            # 创建转诊记录
            referral = Referral(
                patient_id=patient.id,
                from_hospital=claims.get('hospital_name', '未指定'),
                to_hospital=to_hospital,
                doctor_name=doctor_info['name'],
                doctor_phone=doctor_info['phone'],
                status='pending',
                notes=notes,
                image_path=image_path,
                reason='暂无原因'
            )
            db.session.add(referral)
            db.session.commit()

            logger.info(f"Created referral: {referral.id} for patient: {patient.name}")
            return jsonify({
                "msg": "转诊申请提交成功",
                "referral_id": referral.id
            }), 201

        except Exception as e:
            db.session.rollback()
            logger.error(f"Database error: {str(e)}")
            return jsonify({"msg": f"数据库错误: {str(e)}"}), 500

    except Exception as e:
        logger.error(f"Create referral error: {str(e)}")
        return jsonify({"msg": f"创建转诊申请失败: {str(e)}"}), 500

@app.route('/api/patients', methods=['GET'])
@jwt_required()
def get_patients():
    """获取患者列表"""
    try:
        patients = Patient.query.all()
        return jsonify([{
            'id': p.id,
            'name': p.name,
            'age': p.age,
            'gender': p.gender
        } for p in patients])
    except Exception as e:
        logger.error(f"Error fetching patients: {str(e)}")
        return jsonify({"msg": "获取患者列表失败"}), 500

@app.route('/api/hospitals', methods=['GET'])
@jwt_required()
def get_hospitals():
    """获取可用医院列表"""
    try:
        claims = get_jwt()
        role = claims.get('role')

        if role == 'town':
            # 基层医院可以看到所有上级医院
            return jsonify({
                "hospitals": UPPER_HOSPITALS,
                "msg": "获取医院列表成功"
            })
        elif role in ['hospital', 'admin']:
            # 上级医院和管理员可以看到所有医院信息
            return jsonify({
                "hospitals": UPPER_HOSPITALS,
                "hospital_config": HOSPITALS,
                "msg": "获取医院列表成功"
            })
        else:
            return jsonify({"msg": "无权限访问"}), 403

    except Exception as e:
        logger.error(f"Error fetching hospitals: {str(e)}")
        return jsonify({"msg": "获取医院列表失败"}), 500

def _validate_login_data(data: Dict[str, Any]) -> tuple[str, str, bool]:
    """验证登录数据"""
    if not data:
        raise ValueError("未接收到数据")

    username = data.get('username')
    password = data.get('password')
    remember = data.get('remember', False)

    if not username or not password:
        raise ValueError("用户名和密码不能为空")

    return username, password, remember

def _authenticate_user(username: str, password: str) -> User:
    """验证用户身份"""
    user = User.query.filter_by(username=username).first()

    if not user:
        raise ValueError("用户名或密码错误")

    try:
        if not check_password_hash(user.password_hash, password):
            raise ValueError("用户名或密码错误")
    except Exception as e:
        logger.error(f"Password check error: {str(e)}")
        raise ValueError("验证密码时出错")

    return user

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        username, password, remember = _validate_login_data(request.json)
        user = _authenticate_user(username, password)

        # 创建用户信息
        user_info = {
            'username': user.username,
            'role': user.role,
            'hospital_name': user.hospital_name,
            'fullName': getattr(user, 'fullName', username),
            'phone': getattr(user, 'phone', '')
        }

        # 创建访问令牌
        expires = timedelta(days=30) if remember else timedelta(hours=24)
        access_token = create_access_token(
            identity=username,
            additional_claims=user_info,
            expires_delta=expires
        )

        return jsonify({
            "access_token": access_token,
            "user": user_info
        })

    except ValueError as e:
        return jsonify({"msg": str(e)}), 400 if "未接收到数据" in str(e) or "不能为空" in str(e) else 401
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({"msg": "登录失败，请稍后重试"}), 500

@app.route('/api/referrals/<int:referral_id>/review', methods=['PUT'])
@jwt_required()
def review_referral(referral_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        username = get_jwt_identity()
        
        # 检查权限
        if role not in ['hospital', 'admin']:
            return jsonify({"msg": "无权限处理转诊申请"}), 403
        
        # 获取转诊记录
        referral = Referral.query.get(referral_id)
        if not referral:
            return jsonify({"msg": "转诊记录不存在"}), 404
            
        # 获取状态
        status = request.json.get('status')
        if not status or status not in ['accepted', 'rejected', 'transferred', 'recovered']:
            return jsonify({"msg": "无效的状态"}), 400
            
        # 更新转诊状态
        referral.status = status
        referral.reviewed_by = username
        referral.reviewed_at = datetime.now()
        
        # 保存更改
        db.session.commit()

        return jsonify({
            "msg": "转诊申请处理成功", 
            "status": status
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error reviewing referral: {str(e)}")
        return jsonify({"msg": "处理转诊申请失败"}), 500

@app.route('/api/referrals', methods=['GET'])
@jwt_required()
def get_referrals():
    try:
        # 获取用户信息
        claims = get_jwt()
        role = claims.get('role')
        hospital_name = claims.get('hospital_name')
        
        # 根据角色筛选转诊申请
        query = Referral.query.join(Patient)
        
        if role == 'town':
            # 基层医疗机构只能看到自己创建的转诊
            query = query.filter(Referral.from_hospital == hospital_name)
        elif role == 'hospital':
            # 上级医院只能看到明确指定给自己医院的申请
            query = query.filter(Referral.to_hospital == hospital_name)
        # 管理员可以看到所有转诊
        
        # 添加排序：先按状态（待处理优先），再按创建时间倒序
        referrals = query.order_by(
            case(
                (Referral.status == 'pending', 0),
                else_=1
            ),
            Referral.created_at.desc()
        ).all()
        
        # 构建响应数据
        result = []
        for referral in referrals:
            result.append({
                'id': referral.id,
                'from_hospital': referral.from_hospital,
                'to_hospital': referral.to_hospital,
                'doctor_name': referral.doctor_name,
                'doctor_phone': referral.doctor_phone,
                'status': referral.status,
                'created_at': referral.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'reviewed_at': referral.reviewed_at.strftime('%Y-%m-%d %H:%M:%S') if referral.reviewed_at else None,
                'reviewed_by': referral.reviewed_by,
                'review_notes': referral.review_notes,
                'notes': referral.notes,
                'image_path': referral.image_path,
                'reason': referral.reason,
                'patient': {
                    'id': referral.patient.id,
                    'name': referral.patient.name,
                    'age': referral.patient.age,
                    'gender': referral.patient.gender,
                    'medical_history': referral.patient.medical_history
                }
            })
        
        # 计算统计数据
        total = len(result)
        pending = sum(1 for r in result if r['status'] == 'pending')
        accepted = sum(1 for r in result if r['status'] == 'accepted')
        rejected = sum(1 for r in result if r['status'] == 'rejected')
        
        return jsonify({
            'referrals': result,
            'statistics': {
                'total': total,
                'pending': pending,
                'accepted': accepted,
                'rejected': rejected
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting referrals: {str(e)}")
        return jsonify({"msg": f"获取转诊列表失败: {str(e)}"}), 500

@app.route('/api/referrals/<int:referral_id>', methods=['GET'])
@jwt_required()
def get_referral_detail(referral_id):
    try:
        claims = get_jwt()
        referral = Referral.query.get_or_404(referral_id)
        patient = referral.patient

        # 权限检查
        user_role = claims.get('role')
        user_hospital = claims.get('hospital_name')

        if user_role == 'town' and referral.from_hospital != user_hospital:
            return jsonify({"msg": "无权查看此转诊记录"}), 403
        elif user_role == 'hospital' and referral.to_hospital != user_hospital:
            return jsonify({"msg": "无权查看此转诊记录"}), 403

        return jsonify({
            "id": referral.id,
            "patient": {
                "name": patient.name,
                "age": patient.age,
                "gender": patient.gender,
                "medical_history": patient.medical_history,
                "current_condition": patient.current_condition
            },
            "from_hospital": referral.from_hospital,
            "to_hospital": referral.to_hospital,
            "department_contact_name": referral.department_contact_name,
            "doctor_name": referral.doctor_name,
            "doctor_phone": referral.doctor_phone,
            "reason": referral.reason,
            "status": referral.status,
            "created_at": referral.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "reviewed_by": referral.reviewed_by,
            "review_notes": referral.review_notes,
            "reviewed_at": referral.reviewed_at.strftime('%Y-%m-%d %H:%M:%S') if referral.reviewed_at else None
        })

    except Exception as e:
        logger.error(f"Error fetching referral detail: {str(e)}")
        return jsonify({"msg": "获取转诊详情失败"}), 500

@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.json
        
        # 基本字段验证
        required_fields = ['username', 'password', 'fullName', 'phone', 'town']
        if not all(field in data for field in required_fields):
            return jsonify({"msg": "请填写所有必填字段"}), 400
            
        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({"msg": "用户名已存在"}), 400
            
        # 创建新用户
        new_user = User(
            username=data['username'],
            password_hash=generate_password_hash(data['password']),
            fullName=data['fullName'],
            phone=data['phone'],
            hospital_name=data['town'],
            role='town'
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({"msg": "注册成功"}), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Register error: {str(e)}")
        return jsonify({"msg": f"注册失败: {str(e)}"}), 500

@app.route('/user-management')
@jwt_required()
def user_management():
    claims = get_jwt()
    if claims.get('role') != 'admin':
        return redirect('/')
    return render_template('user_management.html')

@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以访问用户列表"}), 403
            
        users = User.query.all()
        users_list = []
        
        for user in users:
            users_list.append({
                'id': user.id,
                'username': user.username,
                'fullName': getattr(user, 'fullName', user.username),
                'hospital_name': user.hospital_name,
                'role': user.role,
                'phone': getattr(user, 'phone', ''),
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else ''
            })
            
        return jsonify({"users": users_list})
        
    except Exception as e:
        logger.error(f"Get users error: {str(e)}")
        return jsonify({"msg": "获取用户列表失败"}), 500

@app.route('/api/users', methods=['POST'])
@jwt_required()
def create_user():
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以创建用户"}), 403
            
        data = request.json
        
        # 验证数据
        required_fields = ['username', 'password', 'hospital_name', 'role', 'fullName', 'phone']
        if not all(field in data for field in required_fields):
            missing_fields = [f for f in required_fields if f not in data]
            return jsonify({"msg": f"缺少必要字段: {', '.join(missing_fields)}"}), 400
            
        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({"msg": "用户名已存在"}), 400
            
        # 创建新用户
        new_user = User(
            username=data['username'],
            password_hash=generate_password_hash(data['password']),
            hospital_name=data['hospital_name'],
            role=data['role'],
            fullName=data['fullName'],
            phone=data['phone']
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            "msg": "用户创建成功",
            "user": {
                "id": new_user.id,
                "username": new_user.username,
                "hospital_name": new_user.hospital_name,
                "role": new_user.role
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Create user error: {str(e)}")
        return jsonify({"msg": f"创建用户失败: {str(e)}"}), 500

@app.route('/test')
def test():
    return jsonify({"status": "ok", "message": "Flask application is running"})

# JWT错误处理
@jwt.invalid_token_loader
def invalid_token_callback(error_string: str):  # 参数由Flask-JWT-Extended提供
    return jsonify({
        'msg': '无效的访问令牌',
        'error': 'invalid_token'
    }), 401

@jwt.expired_token_loader
def expired_token_callback(jwt_header: Dict, jwt_payload: Dict):  # 参数由Flask-JWT-Extended提供
    return jsonify({
        'msg': '访问令牌已过期',
        'error': 'token_expired'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error_string: str):  # 参数由Flask-JWT-Extended提供
    return jsonify({
        'msg': '缺少访问令牌',
        'error': 'authorization_required'
    }), 401

# 添加新的路由用于验证 token 和获取用户信息
@app.route('/api/auth/verify', methods=['GET'])
@jwt_required()
def verify_token():
    try:
        claims = get_jwt()
        return jsonify({
            "valid": True,
            "user": {
                "username": claims.get('username'),
                "role": claims.get('role'),
                "hospital_name": claims.get('hospital_name')
            }
        })
    except Exception as e:
        app.logger.error(f"Token verification error: {str(e)}")
        return jsonify({"valid": False, "msg": "无效的令牌"}), 401

@app.route('/api/referrals/<int:referral_id>', methods=['DELETE'])
@jwt_required()
def delete_referral(referral_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        hospital_name = claims.get('hospital_name')

        # 获取转诊记录
        referral = Referral.query.get_or_404(referral_id)

        # 检查权限：只有创建者（基层医院）或管理员可以删除
        if role != 'admin' and (role != 'town' or referral.from_hospital != hospital_name):
            return jsonify({"msg": "无权删除此转诊记录"}), 403

        # 只能删除待处理的转诊
        if referral.status != 'pending':
            return jsonify({"msg": "只能删除待处理的转诊记录"}), 400

        # 删除关联的患者记录
        patient = Patient.query.get(referral.patient_id)
        if patient:
            db.session.delete(patient)

        # 删除转诊记录
        db.session.delete(referral)
        db.session.commit()

        return jsonify({"msg": "转诊记录已删除"}), 200

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting referral: {str(e)}")
        return jsonify({"msg": "删除转诊记录失败"}), 500

@app.context_processor
def inject_current_time():
    return {'current_time': int(datetime.now().timestamp())}

@app.route('/api/referrals/<int:referral_id>/notes', methods=['PUT'])
@jwt_required()
def update_referral_notes(referral_id):
    try:
        data = request.get_json()
        notes = data.get('notes', '').strip()
        
        referral = Referral.query.get_or_404(referral_id)
        referral.notes = notes
        db.session.commit()
        
        return jsonify({
            "msg": "备注更新成功",
            "notes": notes
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating referral notes: {str(e)}")
        return jsonify({"msg": "备注更新失败"}), 500

# 添加路由以访问上传的文件
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以更新用户信息"}), 403
            
        user = User.query.get_or_404(user_id)
        data = request.json
        
        # 更新用户信息
        if 'fullName' in data:
            user.fullName = data['fullName']
        if 'hospital_name' in data:
            user.hospital_name = data['hospital_name']
        if 'role' in data:
            user.role = data['role']
        if 'phone' in data:
            user.phone = data['phone']
        if 'password' in data and data['password'].strip():
            user.password_hash = generate_password_hash(data['password'])
        
        db.session.commit()
        
        return jsonify({
            "msg": "用户信息更新成功",
            "user": {
                "id": user.id,
                "username": user.username,
                "hospital_name": user.hospital_name,
                "role": user.role
            }
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Update user error: {str(e)}")
        return jsonify({"msg": f"更新用户信息失败: {str(e)}"}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    try:
        claims = get_jwt()
        role = claims.get('role')
        
        # 检查权限
        if role != 'admin':
            return jsonify({"msg": "只有管理员可以删除用户"}), 403
            
        user = User.query.get_or_404(user_id)
        
        # 不允许删除自己
        current_username = get_jwt_identity()
        if user.username == current_username:
            return jsonify({"msg": "不能删除当前登录的用户"}), 400
        
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({"msg": "用户已删除"})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Delete user error: {str(e)}")
        return jsonify({"msg": f"删除用户失败: {str(e)}"}), 500



def init_database():
    """初始化数据库"""
    try:
        # 根据环境选择数据库主机
        db_host = '*************' if os.getenv('FLASK_ENV') == 'production' else '************'

        # 测试数据库连接
        connection = pymysql.connect(
            host=db_host,
            user='root',
            password='windows1',
            port=3306
        )
        connection.close()

        # 创建所有表
        db.create_all()
        logger.info("Database tables created successfully")

    except Exception as e:
        logger.error(f"Database initialization error: {e}")
        raise

# 添加健康检查端点
@app.route('/health')
def health_check():
    """健康检查端点"""
    return jsonify({
        "status": "healthy",
        "timestamp": get_current_time().isoformat(),
        "version": "1.0.0"
    })

@app.route('/test')
def test_endpoint():
    """测试端点"""
    return "OK"

if __name__ == '__main__':
    # 根据环境变量决定配置
    config_name = os.getenv('FLASK_ENV', 'default')
    if config_name == 'production':
        app = create_app('production')

    with app.app_context():
        init_database()

    # 启动应用
    debug_mode = os.getenv('FLASK_ENV') != 'production'
    app.run(host='0.0.0.0', port=5000, debug=debug_mode)
