#!/bin/bash

# 快速修复脚本 - 解决域名指向错误和SSL问题

DOMAIN="referral.beimoyinhenlinlin.cn"
SERVER_IP="*************"

echo "🚀 转诊系统快速修复脚本"
echo "域名: $DOMAIN"
echo "服务器: $SERVER_IP"
echo ""

# 检查当前用户权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

echo "🔍 步骤1: 检查DNS解析..."
DNS_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
echo "当前DNS解析到: $DNS_IP"
echo "应该解析到: $SERVER_IP"

if [ "$DNS_IP" != "$SERVER_IP" ]; then
    echo ""
    echo "❌ DNS解析错误！"
    echo ""
    echo "请按以下步骤修复DNS："
    echo "1. 登录您的域名管理面板（如阿里云、腾讯云等）"
    echo "2. 找到DNS解析设置"
    echo "3. 修改或添加A记录："
    echo "   主机记录: referral"
    echo "   记录类型: A"
    echo "   记录值: $SERVER_IP"
    echo "   TTL: 600"
    echo "4. 保存设置并等待DNS传播（通常5-30分钟）"
    echo ""
    echo "DNS修复后，请重新运行此脚本。"
    exit 1
fi

echo "✅ DNS解析正确"
echo ""

echo "🔍 步骤2: 检查服务器上的服务..."

# 检查应用是否运行
if supervisorctl status referral-system | grep -q "RUNNING"; then
    echo "✅ 转诊应用正在运行"
else
    echo "⚠️  转诊应用未运行，尝试启动..."
    supervisorctl start referral-system
    sleep 3
    if supervisorctl status referral-system | grep -q "RUNNING"; then
        echo "✅ 转诊应用启动成功"
    else
        echo "❌ 转诊应用启动失败"
        echo "请检查日志: tail -f /var/www/referral/logs/app.log"
    fi
fi

# 检查Nginx
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx正在运行"
else
    echo "⚠️  Nginx未运行，尝试启动..."
    systemctl start nginx
    if systemctl is-active --quiet nginx; then
        echo "✅ Nginx启动成功"
    else
        echo "❌ Nginx启动失败"
        echo "请检查配置: nginx -t"
    fi
fi

echo ""
echo "🔍 步骤3: 检查端口监听..."
if netstat -tlnp | grep -q ":5000"; then
    echo "✅ 应用端口5000正在监听"
else
    echo "❌ 应用端口5000未监听"
fi

if netstat -tlnp | grep -q ":80"; then
    echo "✅ HTTP端口80正在监听"
else
    echo "❌ HTTP端口80未监听"
fi

echo ""
echo "🔍 步骤4: 测试HTTP访问..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN/health 2>/dev/null || echo "000")
echo "HTTP状态码: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ HTTP访问正常"
    echo "可以访问: http://$DOMAIN"
else
    echo "❌ HTTP访问异常"
    echo ""
    echo "可能的问题："
    echo "1. 应用未正确启动"
    echo "2. Nginx配置错误"
    echo "3. 防火墙阻止访问"
    echo "4. 其他服务占用了端口"
    echo ""
    echo "建议运行完整修复脚本: ./fix_domain_ssl.sh"
fi

echo ""
echo "🔍 步骤5: 检查SSL证书..."
if command -v openssl >/dev/null 2>&1; then
    SSL_INFO=$(echo | timeout 5 openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject 2>/dev/null)
    if [ -n "$SSL_INFO" ]; then
        echo "SSL证书存在: $SSL_INFO"
        HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/health 2>/dev/null || echo "000")
        if [ "$HTTPS_STATUS" = "200" ]; then
            echo "✅ HTTPS访问正常"
            echo "可以访问: https://$DOMAIN"
        else
            echo "⚠️  HTTPS访问异常，可能需要重新配置SSL"
        fi
    else
        echo "⚠️  SSL证书不存在或无效"
        echo "建议运行: certbot --nginx -d $DOMAIN"
    fi
else
    echo "⚠️  无法检查SSL（openssl未安装）"
fi

echo ""
echo "📋 快速修复建议："
echo ""

if [ "$HTTP_STATUS" != "200" ]; then
    echo "🔧 修复HTTP访问问题："
    echo "   sudo ./fix_domain_ssl.sh"
    echo ""
fi

echo "🔧 如果需要重新配置SSL："
echo "   sudo certbot delete --cert-name $DOMAIN"
echo "   sudo certbot --nginx -d $DOMAIN"
echo ""

echo "🔧 如果应用无法启动："
echo "   sudo ./fix_permissions.sh"
echo ""

echo "🔧 查看详细日志："
echo "   sudo tail -f /var/www/referral/logs/app.log"
echo "   sudo tail -f /var/log/nginx/error.log"
echo ""

echo "🔧 重启所有服务："
echo "   sudo supervisorctl restart referral-system"
echo "   sudo systemctl restart nginx"
echo ""

echo "完成检查。如果问题仍然存在，请运行相应的修复脚本。"
