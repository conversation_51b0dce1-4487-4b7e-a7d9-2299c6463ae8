# 环境配置示例文件
# 复制此文件为 .env 并修改相应的值

# Flask环境
FLASK_ENV=production

# 数据库配置
DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/referral_system
USER_DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/user_system

# JWT密钥 (生产环境请使用强密钥)
JWT_SECRET_KEY=referral-system-jwt-secret-2024

# 域名配置
DOMAIN=referral.beimoyinhenlinlin.cn

# 服务器配置
SERVER_IP=*************

# 邮件配置 (可选)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=static/uploads
