# 转诊系统生产环境部署总结

## 🎯 部署目标
- **服务器**: *************
- **域名**: https://referral.beimoyinhenlinlin.cn
- **数据库**: MySQL (*************:3306)
- **SSL**: Let's Encrypt自动证书

## 📦 部署文件清单

### 核心应用文件
- ✅ `app.py` - 主应用文件（已优化生产环境配置）
- ✅ `config.py` - 配置文件（已更新生产环境数据库）
- ✅ `wsgi.py` - WSGI入口文件
- ✅ `requirements.txt` - Python依赖包
- ✅ `gunicorn.conf.py` - Gunicorn配置

### 部署脚本
- ✅ `deploy.sh` - 自动化部署脚本
- ✅ `manage.sh` - 生产环境管理脚本
- ✅ `migrate_hospitals.py` - 数据库迁移脚本（已更新服务器IP）

### 配置文件
- ✅ `.env.example` - 环境变量示例
- ✅ `DEPLOYMENT_GUIDE.md` - 详细部署指南

### 前端文件
- ✅ `templates/index.html` - 已添加医院选择功能
- ✅ `static/js/main.js` - 已优化医院选择逻辑
- ✅ `static/css/style.css` - 已添加医院标识样式

## 🚀 快速部署步骤

### 1. 上传文件到服务器
```bash
# 使用scp上传（在本地执行）
scp -r . root@*************:/tmp/referral-system/

# 或使用rsync
rsync -avz --exclude='.git' --exclude='__pycache__' . root@*************:/tmp/referral-system/
```

### 2. 在服务器上执行部署
```bash
# SSH登录服务器
ssh root@*************

# 进入项目目录
cd /tmp/referral-system

# 给脚本执行权限
chmod +x deploy.sh manage.sh

# 运行自动部署脚本
./deploy.sh
```

### 3. 验证部署
访问以下地址确认部署成功：
- https://referral.beimoyinhenlinlin.cn
- https://referral.beimoyinhenlinlin.cn/health

## 🔧 主要配置更改

### 1. 数据库配置更新
```python
# 生产环境数据库主机更改为
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:windows1@*************:3306/referral_system'
```

### 2. CORS配置增强
```python
# 添加生产域名到允许列表
allowed_origins = [
    "https://referral.beimoyinhenlinlin.cn",
    "http://referral.beimoyinhenlinlin.cn"
]
```

### 3. 安全配置加强
```python
# 生产环境安全设置
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
JWT_SECRET_KEY = 'referral-system-jwt-secret-2024'
```

## 🏥 医院配置

系统支持3个总医院：
1. **涟源市妇幼保健院** - 粉红色标识
2. **涟源市人民医院** - 蓝色标识  
3. **涟源市中医院** - 绿色标识

### 默认管理员账户
| 医院 | 用户名 | 密码 | 角色 |
|------|--------|------|------|
| 涟源市妇幼保健院 | maternity_admin | 123456 | hospital |
| 涟源市人民医院 | peoples_admin | 123456 | hospital |
| 涟源市中医院 | tcm_admin | 123456 | hospital |

## 🛠 部署后管理

### 常用管理命令
```bash
# 查看应用状态
sudo supervisorctl status referral-system

# 重启应用
sudo supervisorctl restart referral-system

# 查看日志
sudo tail -f /var/www/referral/logs/app.log

# 使用管理脚本
/var/www/referral/manage.sh status
/var/www/referral/manage.sh restart
/var/www/referral/manage.sh logs
```

### 数据库备份
```bash
# 手动备份
/var/www/referral/manage.sh backup

# 设置自动备份（每天凌晨3点）
echo "0 3 * * * /var/www/referral/manage.sh backup" | sudo crontab -
```

## 🔒 安全措施

### 1. SSL证书
- 自动获取Let's Encrypt证书
- 强制HTTPS重定向
- 安全头配置

### 2. 防火墙配置
```bash
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 3. 应用安全
- JWT令牌认证
- 安全的Cookie设置
- CORS限制
- 输入验证

## 📊 监控和日志

### 日志文件位置
- 应用日志: `/var/www/referral/logs/app.log`
- Nginx访问日志: `/var/log/nginx/access.log`
- Nginx错误日志: `/var/log/nginx/error.log`

### 健康检查
- 健康检查端点: `/health`
- 测试端点: `/test`
- 应用状态监控: Supervisor

## 🔄 更新流程

### 代码更新
```bash
# 使用管理脚本
/var/www/referral/manage.sh update

# 或手动更新
sudo supervisorctl stop referral-system
cd /var/www/referral
sudo -u referral git pull
sudo -u referral ./venv/bin/pip install -r requirements.txt
sudo supervisorctl start referral-system
```

### 数据库迁移
```bash
/var/www/referral/manage.sh migrate
```

## ⚠️ 重要提醒

### 部署前检查
1. ✅ 确保域名DNS已指向服务器IP
2. ✅ 确保服务器防火墙允许80和443端口
3. ✅ 确保数据库服务正常运行
4. ✅ 确保有服务器root权限

### 部署后必做
1. 🔐 立即修改所有默认密码
2. 🔒 配置防火墙规则
3. 📧 设置SSL证书自动更新
4. 💾 配置数据库自动备份
5. 📊 设置监控和告警

## 📞 故障排除

### 常见问题
1. **应用无法启动**: 检查日志 `/var/www/referral/logs/app.log`
2. **数据库连接失败**: 验证数据库服务和网络连接
3. **SSL证书问题**: 检查域名DNS和防火墙设置
4. **权限错误**: 确保文件属主为referral用户

### 紧急恢复
```bash
# 恢复应用
sudo supervisorctl restart referral-system

# 恢复Nginx
sudo systemctl restart nginx

# 恢复数据库（从备份）
mysql -h ************* -u root -p < backup_file.sql
```

## 🎉 部署完成

部署完成后，转诊系统将在以下地址运行：
- **主页**: https://referral.beimoyinhenlinlin.cn
- **管理界面**: 使用默认账户登录后访问

系统现在支持：
- ✅ 3个具体医院的转诊选择
- ✅ 基于角色的权限控制
- ✅ HTTPS安全访问
- ✅ 自动SSL证书管理
- ✅ 完整的日志和监控
- ✅ 数据库备份机制

祝您部署顺利！🚀
