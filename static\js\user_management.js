// 显示用户列表
async function loadUsers() {
    try {
        const response = await fetch('/api/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('获取用户列表失败');
        }

        const users = await response.json();
        displayUsers(users);
    } catch (error) {
        console.error('Error loading users:', error);
        showToast(error.message, 'danger');
    }
}

// 显示用户列表
function displayUsers(users) {
    const tbody = document.getElementById('userTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';
    users.forEach(user => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.hospital_name}</td>
            <td>${user.role === 'hospital' ? '上级医院' : '基层医疗机构'}</td>
            <td>${new Date(user.created_at).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 显示新建用户表单
function showNewUserForm() {
    const modal = new bootstrap.Modal(document.getElementById('userForm'));
    modal.show();
}

// 处理角色变更
function handleRoleChange(select) {
    const hospitalNameField = document.getElementById('hospitalNameField');
    const hospitalNameInput = document.querySelector('input[name="hospital_name"]');
    
    if (select.value === 'hospital') {
        hospitalNameInput.value = '涟源市妇幼保健院';
        hospitalNameField.style.display = 'none';
    } else {
        hospitalNameInput.value = '';
        hospitalNameField.style.display = 'block';
    }
}

// 处理新建用户
async function handleNewUser(event) {
    event.preventDefault();
    const form = event.target;

    try {
        const userData = {
            username: form.username.value.trim(),
            password: form.password.value,
            role: form.role.value,
            hospital_name: form.hospital_name.value.trim()
        };

        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(userData)
        });

        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.msg || '创建用户失败');
        }

        showToast('用户创建成功', 'success');
        bootstrap.Modal.getInstance(document.getElementById('userForm')).hide();
        form.reset();
        await loadUsers();
    } catch (error) {
        console.error('Error creating user:', error);
        showToast(error.message, 'danger');
    }
}

// 删除用户
async function deleteUser(userId) {
    if (!confirm('确定要删除此用户吗？')) return;

    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('删除用户失败');
        }

        showToast('用户删除成功', 'success');
        await loadUsers();
    } catch (error) {
        console.error('Error deleting user:', error);
        showToast(error.message, 'danger');
    }
}

// 页面加载时获取用户列表
document.addEventListener('DOMContentLoaded', loadUsers); 