from docx import Document
import re

answer_text = (" ### 正确答案  "
               "**E. TSH**  "
               "### 详细说明 ")


correct_answer = ""
match = re.search(
                    r'###\s*正确答案\s*[\s*]*([A-Ea-e])',  # 关键修改点
                    answer_text,
                    re.IGNORECASE | re.MULTILINE          # 启用多行和忽略大小写
                )

if match:
    correct_answer = match.group(1).upper()
else:
                    # 处理未匹配的情况，例如记录日志或抛出异常
    print(f"未匹配到正确答案：{answer_text}")


print(correct_answer)