#!/bin/bash

# 简化版转诊系统部署脚本
# 解决权限问题的版本

set -e

# 配置变量
SERVER_IP="*************"
DOMAIN="referral.beimoyinhenlinlin.cn"
APP_DIR="/var/www/referral"
SERVICE_NAME="referral-system"
APP_USER="referral"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root权限运行此脚本"
        exit 1
    fi
}

# 安装基础依赖
install_dependencies() {
    print_status "更新系统并安装依赖..."
    
    apt update
    apt install -y python3 python3-pip python3-venv nginx supervisor mysql-client git curl wget
    
    print_status "基础依赖安装完成"
}

# 停止可能运行的服务
stop_services() {
    print_status "停止现有服务..."
    
    systemctl stop nginx || true
    supervisorctl stop $SERVICE_NAME || true
    
    print_status "服务停止完成"
}

# 清理并重新创建应用环境
setup_app_environment() {
    print_status "设置应用环境..."
    
    # 删除现有目录
    if [ -d "$APP_DIR" ]; then
        rm -rf "$APP_DIR"
    fi
    
    # 删除现有用户
    if id "$APP_USER" &>/dev/null; then
        userdel -r "$APP_USER" || true
    fi
    
    # 创建新用户
    useradd -r -s /bin/bash -d "$APP_DIR" -m "$APP_USER"
    
    # 创建目录结构
    mkdir -p "$APP_DIR"/{logs,static/uploads,backups}
    
    # 复制应用文件
    cp -r /tmp/referral-system/* "$APP_DIR/"
    
    # 设置权限
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    chmod -R 755 "$APP_DIR"
    
    print_status "应用环境设置完成"
}

# 创建Python环境
setup_python_environment() {
    print_status "设置Python环境..."
    
    # 切换到应用目录
    cd "$APP_DIR"
    
    # 以应用用户身份创建虚拟环境
    sudo -u "$APP_USER" python3 -m venv venv
    
    # 确保虚拟环境权限正确
    chown -R "$APP_USER:$APP_USER" venv
    chmod -R 755 venv
    
    # 升级pip并安装依赖
    sudo -u "$APP_USER" venv/bin/python -m pip install --upgrade pip
    sudo -u "$APP_USER" venv/bin/pip install -r requirements.txt
    
    print_status "Python环境设置完成"
}

# 创建环境配置
create_environment_config() {
    print_status "创建环境配置..."
    
    cat > "$APP_DIR/.env" << EOF
FLASK_ENV=production
DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/referral_system
USER_DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/user_system
JWT_SECRET_KEY=referral-system-jwt-secret-2024
EOF
    
    chown "$APP_USER:$APP_USER" "$APP_DIR/.env"
    chmod 600 "$APP_DIR/.env"
    
    print_status "环境配置创建完成"
}

# 配置Supervisor
configure_supervisor() {
    print_status "配置Supervisor..."
    
    cat > /etc/supervisor/conf.d/$SERVICE_NAME.conf << EOF
[program:$SERVICE_NAME]
command=$APP_DIR/venv/bin/python app.py
directory=$APP_DIR
user=$APP_USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$APP_DIR/logs/app.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=FLASK_ENV=production
EOF
    
    print_status "Supervisor配置完成"
}

# 配置Nginx（简化版，不包含SSL）
configure_nginx() {
    print_status "配置Nginx..."
    
    cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    location /static/ {
        alias $APP_DIR/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        access_log off;
    }
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t
    
    print_status "Nginx配置完成"
}

# 运行数据库迁移
run_database_migration() {
    print_status "运行数据库迁移..."
    
    cd "$APP_DIR"
    sudo -u "$APP_USER" venv/bin/python migrate_hospitals.py
    
    print_status "数据库迁移完成"
}

# 启动服务
start_services() {
    print_status "启动服务..."
    
    # 重新加载Supervisor配置
    supervisorctl reread
    supervisorctl update
    
    # 启动应用
    supervisorctl start $SERVICE_NAME
    
    # 启动Nginx
    systemctl start nginx
    systemctl enable nginx
    systemctl enable supervisor
    
    print_status "服务启动完成"
}

# 检查部署状态
check_deployment() {
    print_status "检查部署状态..."
    
    sleep 5  # 等待服务启动
    
    # 检查应用状态
    if supervisorctl status $SERVICE_NAME | grep -q "RUNNING"; then
        print_status "✅ 应用服务运行正常"
    else
        print_error "❌ 应用服务未运行"
        supervisorctl status $SERVICE_NAME
    fi
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        print_status "✅ Nginx服务运行正常"
    else
        print_error "❌ Nginx服务未运行"
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":5000"; then
        print_status "✅ 应用端口5000监听正常"
    else
        print_warning "⚠️  应用端口5000未监听"
    fi
    
    print_status "部署状态检查完成"
}

# 主部署流程
main() {
    print_status "开始简化部署流程..."
    
    check_root
    install_dependencies
    stop_services
    setup_app_environment
    setup_python_environment
    create_environment_config
    configure_supervisor
    configure_nginx
    run_database_migration
    start_services
    check_deployment
    
    echo ""
    print_status "🎉 基础部署完成！"
    echo ""
    echo "访问地址: http://$DOMAIN (HTTP版本)"
    echo ""
    echo "管理命令:"
    echo "  查看应用日志: tail -f $APP_DIR/logs/app.log"
    echo "  重启应用: supervisorctl restart $SERVICE_NAME"
    echo "  查看应用状态: supervisorctl status $SERVICE_NAME"
    echo ""
    echo "如需启用HTTPS，请运行:"
    echo "  apt install certbot python3-certbot-nginx"
    echo "  certbot --nginx -d $DOMAIN"
    echo ""
    print_status "默认医院管理员账户:"
    echo "  涟源市妇幼保健院: maternity_admin / 123456"
    echo "  涟源市人民医院: peoples_admin / 123456"
    echo "  涟源市中医院: tcm_admin / 123456"
}

# 执行主流程
main "$@"
