# 转诊系统部署故障排除指南

## 🚨 当前遇到的权限问题

### 问题描述
```
Unable to symlink '/usr/bin/python3' to '/var/www/referral/venv/bin/python3'
sudo: unable to execute /var/www/referral/venv/bin/pip: Permission denied
```

### 问题原因
1. 虚拟环境创建时权限设置不正确
2. 应用用户没有足够的权限访问虚拟环境
3. 文件系统权限问题

## 🔧 解决方案

### 方案1: 使用权限修复脚本（推荐）

```bash
# 在服务器上执行
cd /tmp/referral-system

# 运行权限修复脚本
chmod +x fix_permissions.sh
sudo ./fix_permissions.sh
```

### 方案2: 使用简化部署脚本

```bash
# 在服务器上执行
cd /tmp/referral-system

# 运行简化部署脚本（会重新创建所有环境）
chmod +x deploy_simple.sh
sudo ./deploy_simple.sh
```

### 方案3: 手动修复步骤

```bash
# 1. 停止现有服务
sudo supervisorctl stop referral-system || true
sudo systemctl stop nginx || true

# 2. 删除现有虚拟环境
sudo rm -rf /var/www/referral/venv

# 3. 确保用户存在
sudo useradd -r -s /bin/bash -d /var/www/referral referral || true

# 4. 设置目录权限
sudo chown -R referral:referral /var/www/referral
sudo chmod -R 755 /var/www/referral

# 5. 重新创建虚拟环境
cd /var/www/referral
sudo -u referral python3 -m venv venv

# 6. 设置虚拟环境权限
sudo chown -R referral:referral venv
sudo chmod -R 755 venv

# 7. 安装依赖
sudo -u referral venv/bin/pip install --upgrade pip
sudo -u referral venv/bin/pip install -r requirements.txt

# 8. 重启服务
sudo supervisorctl start referral-system
sudo systemctl start nginx
```

## 🔍 常见问题诊断

### 1. 检查用户和权限

```bash
# 检查用户是否存在
id referral

# 检查目录权限
ls -la /var/www/referral

# 检查虚拟环境权限
ls -la /var/www/referral/venv/bin/
```

### 2. 检查Python环境

```bash
# 检查系统Python
which python3
python3 --version

# 检查虚拟环境Python
sudo -u referral /var/www/referral/venv/bin/python --version
sudo -u referral /var/www/referral/venv/bin/pip --version
```

### 3. 检查服务状态

```bash
# 检查Supervisor状态
sudo supervisorctl status

# 检查Nginx状态
sudo systemctl status nginx

# 检查端口监听
netstat -tlnp | grep -E ":(80|443|5000)"
```

### 4. 查看日志

```bash
# 应用日志
sudo tail -f /var/www/referral/logs/app.log

# Supervisor日志
sudo tail -f /var/log/supervisor/supervisord.log

# Nginx日志
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -f -u supervisor
```

## 🛠 其他常见问题

### 问题1: 数据库连接失败

**症状**: 应用启动失败，日志显示数据库连接错误

**解决方案**:
```bash
# 测试数据库连接
mysql -h ************* -u root -p

# 检查数据库是否存在
mysql -h ************* -u root -p -e "SHOW DATABASES;"

# 创建数据库（如果不存在）
mysql -h ************* -u root -p -e "CREATE DATABASE IF NOT EXISTS referral_system;"
mysql -h ************* -u root -p -e "CREATE DATABASE IF NOT EXISTS user_system;"
```

### 问题2: Nginx配置错误

**症状**: Nginx启动失败或配置测试失败

**解决方案**:
```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 重新生成配置
sudo rm /etc/nginx/sites-enabled/referral.beimoyinhenlinlin.cn
# 然后重新运行部署脚本的Nginx配置部分
```

### 问题3: SSL证书问题

**症状**: HTTPS访问失败

**解决方案**:
```bash
# 检查域名DNS解析
nslookup referral.beimoyinhenlinlin.cn

# 手动获取SSL证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d referral.beimoyinhenlinlin.cn

# 检查证书状态
sudo certbot certificates
```

### 问题4: 应用无法启动

**症状**: Supervisor显示应用状态为FATAL

**解决方案**:
```bash
# 手动测试应用启动
cd /var/www/referral
sudo -u referral venv/bin/python app.py

# 检查Python依赖
sudo -u referral venv/bin/pip list

# 重新安装依赖
sudo -u referral venv/bin/pip install -r requirements.txt --force-reinstall
```

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器有root权限
- [ ] 域名DNS已指向服务器IP
- [ ] 服务器防火墙允许80和443端口
- [ ] 数据库服务正常运行
- [ ] 网络连接正常

### 部署后检查
- [ ] 应用服务运行正常 (`supervisorctl status referral-system`)
- [ ] Nginx服务运行正常 (`systemctl status nginx`)
- [ ] 端口监听正常 (`netstat -tlnp | grep 5000`)
- [ ] 数据库连接正常
- [ ] 网站可以正常访问
- [ ] 日志没有错误信息

## 🆘 紧急恢复步骤

如果部署完全失败，可以使用以下步骤重新开始：

```bash
# 1. 完全清理
sudo supervisorctl stop referral-system || true
sudo systemctl stop nginx || true
sudo rm -rf /var/www/referral
sudo userdel -r referral || true
sudo rm -f /etc/supervisor/conf.d/referral-system.conf
sudo rm -f /etc/nginx/sites-enabled/referral.beimoyinhenlinlin.cn
sudo rm -f /etc/nginx/sites-available/referral.beimoyinhenlinlin.cn

# 2. 重新部署
cd /tmp/referral-system
sudo ./deploy_simple.sh
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **错误日志**:
   ```bash
   sudo tail -n 50 /var/www/referral/logs/app.log
   sudo tail -n 50 /var/log/nginx/error.log
   ```

2. **系统信息**:
   ```bash
   uname -a
   cat /etc/os-release
   python3 --version
   ```

3. **服务状态**:
   ```bash
   sudo supervisorctl status
   sudo systemctl status nginx
   netstat -tlnp | grep -E ":(80|443|5000)"
   ```

4. **权限信息**:
   ```bash
   ls -la /var/www/referral/
   ls -la /var/www/referral/venv/bin/
   ```

这些信息将帮助快速定位和解决问题。
