<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-users-cog me-2"></i>用户管理系统
            </a>
            <div class="d-flex">
                <a href="/" class="btn btn-outline-light me-2">返回主系统</a>
                <button class="btn btn-light" onclick="logout()">退出</button>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 用户列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>用户列表</h5>
                <button class="btn btn-primary" onclick="showNewUserForm()">
                    <i class="fas fa-user-plus me-2"></i>新建用户
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>医院名称</th>
                                <th>角色</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 新建用户表单 -->
        <div class="modal fade" id="userForm">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">新建用户</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form onsubmit="handleNewUser(event)">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">用户名</label>
                                <input type="text" name="username" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">密码</label>
                                <input type="password" name="password" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">医院类型</label>
                                <select name="role" class="form-select" required onchange="handleRoleChange(this)">
                                    <option value="">选择医院类型</option>
                                    <option value="hospital">涟源市妇幼保健院</option>
                                    <option value="town">基层医疗机构</option>
                                </select>
                            </div>
                            <div class="mb-3" id="hospitalNameField">
                                <label class="form-label">医院名称</label>
                                <input type="text" name="hospital_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/user_management.js') }}"></script>
</body>
</html> 