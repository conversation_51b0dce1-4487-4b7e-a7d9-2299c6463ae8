import pymysql
from app import create_app, db
from werkzeug.security import generate_password_hash
import os

def reset_databases():
    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host='************',
            user='root',
            password='windows1',
            port=3306
        )
        cursor = connection.cursor()
        
        # 删除现有数据库（如果存在）
        try:
            cursor.execute("DROP DATABASE IF EXISTS referral_system")
            cursor.execute("DROP DATABASE IF EXISTS user_system")
            print("旧数据库已删除。")
            
            # 创建新数据库
            cursor.execute("CREATE DATABASE referral_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            cursor.execute("CREATE DATABASE user_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            connection.commit()
            print("新数据库已创建。")
        except Exception as e:
            print(f"创建数据库时出错: {e}")
            raise
        finally:
            cursor.close()
            connection.close()

        # 创建应用上下文并创建表
        app = create_app('development')
        with app.app_context():
            # 确保上传目录存在
            upload_folder = os.path.join(app.root_path, 'static/uploads')
            os.makedirs(upload_folder, exist_ok=True)
            print(f"上传目录已创建: {upload_folder}")
            
            db.create_all()
            print("表已创建。")

            # 创建一个默认管理员用户
            try:
                from app import User
                admin = User(
                    username='admin',
                    password_hash=generate_password_hash('admin123'),
                    hospital_name='系统管理员',
                    role='admin',
                    fullName='系统管理员',
                    phone='13800000000'
                )
                db.session.add(admin)
                db.session.commit()
                print("默认管理员用户已创建。")
            except Exception as e:
                print(f"创建管理员用户时出错: {e}")
                db.session.rollback()

    except Exception as e:
        print(f"数据库重置错误: {e}")
        raise

if __name__ == '__main__':
    reset_databases() 