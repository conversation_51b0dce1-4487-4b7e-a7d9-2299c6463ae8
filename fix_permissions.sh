#!/bin/bash

# 修复转诊系统部署权限问题

set -e

APP_DIR="/var/www/referral"
APP_USER="referral"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root权限运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root权限运行此脚本"
        echo "使用: sudo $0"
        exit 1
    fi
}

# 清理可能存在的虚拟环境
cleanup_venv() {
    print_status "清理现有虚拟环境..."
    
    if [ -d "$APP_DIR/venv" ]; then
        rm -rf "$APP_DIR/venv"
        print_status "已删除现有虚拟环境"
    fi
}

# 确保用户存在
ensure_user() {
    print_status "检查应用用户..."
    
    if ! id "$APP_USER" &>/dev/null; then
        useradd -r -s /bin/false -d "$APP_DIR" "$APP_USER"
        print_status "创建用户: $APP_USER"
    else
        print_status "用户 $APP_USER 已存在"
    fi
}

# 创建目录结构
create_directories() {
    print_status "创建目录结构..."
    
    mkdir -p "$APP_DIR"
    mkdir -p "$APP_DIR/logs"
    mkdir -p "$APP_DIR/static/uploads"
    mkdir -p "$APP_DIR/backups"
    
    print_status "目录创建完成"
}

# 设置正确的权限
fix_permissions() {
    print_status "修复文件权限..."
    
    # 设置目录所有者
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    
    # 设置目录权限
    chmod -R 755 "$APP_DIR"
    
    # 设置日志目录权限
    chmod 755 "$APP_DIR/logs"
    
    # 设置上传目录权限
    chmod 755 "$APP_DIR/static/uploads"
    
    # 如果存在.env文件，设置安全权限
    if [ -f "$APP_DIR/.env" ]; then
        chmod 600 "$APP_DIR/.env"
        chown "$APP_USER:$APP_USER" "$APP_DIR/.env"
    fi
    
    print_status "权限修复完成"
}

# 重新创建虚拟环境
create_venv() {
    print_status "创建Python虚拟环境..."
    
    # 切换到应用用户创建虚拟环境
    sudo -u "$APP_USER" python3 -m venv "$APP_DIR/venv"
    
    # 确保虚拟环境文件权限正确
    chown -R "$APP_USER:$APP_USER" "$APP_DIR/venv"
    chmod -R 755 "$APP_DIR/venv"
    
    # 给pip和python执行权限
    chmod +x "$APP_DIR/venv/bin/pip"
    chmod +x "$APP_DIR/venv/bin/python"
    chmod +x "$APP_DIR/venv/bin/python3"
    
    print_status "虚拟环境创建完成"
}

# 安装Python依赖
install_dependencies() {
    print_status "安装Python依赖..."
    
    # 升级pip
    sudo -u "$APP_USER" "$APP_DIR/venv/bin/python" -m pip install --upgrade pip
    
    # 安装依赖
    if [ -f "$APP_DIR/requirements.txt" ]; then
        sudo -u "$APP_USER" "$APP_DIR/venv/bin/pip" install -r "$APP_DIR/requirements.txt"
        print_status "依赖安装完成"
    else
        print_warning "requirements.txt 文件不存在，跳过依赖安装"
    fi
}

# 创建环境变量文件
create_env_file() {
    print_status "创建环境变量文件..."
    
    if [ ! -f "$APP_DIR/.env" ]; then
        cat > "$APP_DIR/.env" << EOF
FLASK_ENV=production
DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/referral_system
USER_DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/user_system
JWT_SECRET_KEY=referral-system-jwt-secret-2024
EOF
        
        chown "$APP_USER:$APP_USER" "$APP_DIR/.env"
        chmod 600 "$APP_DIR/.env"
        print_status "环境变量文件创建完成"
    else
        print_status "环境变量文件已存在"
    fi
}

# 测试虚拟环境
test_venv() {
    print_status "测试虚拟环境..."
    
    # 测试Python
    if sudo -u "$APP_USER" "$APP_DIR/venv/bin/python" --version; then
        print_status "✅ Python测试通过"
    else
        print_error "❌ Python测试失败"
        return 1
    fi
    
    # 测试pip
    if sudo -u "$APP_USER" "$APP_DIR/venv/bin/pip" --version; then
        print_status "✅ pip测试通过"
    else
        print_error "❌ pip测试失败"
        return 1
    fi
    
    print_status "虚拟环境测试完成"
}

# 主函数
main() {
    print_status "开始修复权限问题..."
    
    check_root
    cleanup_venv
    ensure_user
    create_directories
    fix_permissions
    create_venv
    install_dependencies
    create_env_file
    test_venv
    
    print_status "🎉 权限问题修复完成！"
    echo ""
    echo "现在可以继续部署流程："
    echo "1. 确保所有应用文件都在 $APP_DIR 目录中"
    echo "2. 运行: sudo supervisorctl restart referral-system"
    echo "3. 检查状态: sudo supervisorctl status referral-system"
}

# 执行主函数
main "$@"
