# 前端医院选择功能实现指南

## 概述
为了支持3个总医院（涟源市妇幼保健院、涟源市人民医院、涟源市中医院），需要在前端添加医院选择功能。

## 需要修改的前端文件

### 1. 转诊申请表单 (templates/index.html 或相关模板)

#### 添加医院选择下拉框
```html
<!-- 在患者信息表单中添加目标医院选择 -->
<div class="form-group">
    <label for="toHospital">转诊目标医院 *</label>
    <select id="toHospital" name="toHospital" class="form-control" required>
        <option value="">请选择医院</option>
        <option value="涟源市妇幼保健院">涟源市妇幼保健院</option>
        <option value="涟源市人民医院">涟源市人民医院</option>
        <option value="涟源市中医院">涟源市中医院</option>
    </select>
</div>
```

#### JavaScript 动态加载医院列表
```javascript
// 获取医院列表的函数
async function loadHospitals() {
    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch('/api/hospitals', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            const hospitalSelect = document.getElementById('toHospital');
            
            // 清空现有选项
            hospitalSelect.innerHTML = '<option value="">请选择医院</option>';
            
            // 添加医院选项
            data.hospitals.forEach(hospital => {
                const option = document.createElement('option');
                option.value = hospital;
                option.textContent = hospital;
                hospitalSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载医院列表失败:', error);
    }
}

// 页面加载时调用
document.addEventListener('DOMContentLoaded', function() {
    loadHospitals();
});
```

### 2. 转诊申请提交逻辑修改

#### 表单提交 (FormData 方式)
```javascript
function submitReferral() {
    const formData = new FormData();
    
    // 患者信息
    formData.append('patientName', document.getElementById('patientName').value);
    formData.append('patientAge', document.getElementById('patientAge').value);
    formData.append('patientGender', document.getElementById('patientGender').value);
    
    // 目标医院
    formData.append('toHospital', document.getElementById('toHospital').value);
    
    // 备注
    formData.append('notes', document.getElementById('notes').value);
    
    // 医疗图片
    const imageFile = document.getElementById('medicalImage').files[0];
    if (imageFile) {
        formData.append('medicalImage', imageFile);
    }
    
    // 提交请求
    fetch('/api/referrals', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.msg) {
            alert(data.msg);
            if (data.referral_id) {
                // 成功后刷新页面或跳转
                location.reload();
            }
        }
    })
    .catch(error => {
        console.error('提交失败:', error);
        alert('提交失败，请重试');
    });
}
```

#### JSON 提交方式
```javascript
function submitReferralJSON() {
    const referralData = {
        patient_info: {
            name: document.getElementById('patientName').value,
            age: parseInt(document.getElementById('patientAge').value),
            gender: document.getElementById('patientGender').value
        },
        to_hospital: document.getElementById('toHospital').value,
        notes: document.getElementById('notes').value
    };
    
    fetch('/api/referrals', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(referralData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.msg) {
            alert(data.msg);
            if (data.referral_id) {
                location.reload();
            }
        }
    })
    .catch(error => {
        console.error('提交失败:', error);
        alert('提交失败，请重试');
    });
}
```

### 3. 转诊列表显示优化

#### 在转诊列表中显示具体医院名称
```javascript
function renderReferralList(referrals) {
    const tbody = document.getElementById('referralTableBody');
    tbody.innerHTML = '';
    
    referrals.forEach(referral => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${referral.id}</td>
            <td>${referral.patient.name}</td>
            <td>${referral.from_hospital}</td>
            <td><span class="badge badge-info">${referral.to_hospital}</span></td>
            <td>${referral.doctor_name}</td>
            <td>${getStatusBadge(referral.status)}</td>
            <td>${referral.created_at}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="viewReferral(${referral.id})">
                    查看详情
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function getStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="badge badge-warning">待处理</span>',
        'accepted': '<span class="badge badge-success">已接诊</span>',
        'rejected': '<span class="badge badge-danger">未接诊</span>',
        'transferred': '<span class="badge badge-info">已转院</span>',
        'recovered': '<span class="badge badge-secondary">已康复</span>'
    };
    return statusMap[status] || '<span class="badge badge-light">未知</span>';
}
```

### 4. 用户权限相关的界面调整

#### 基层医院用户界面
```javascript
// 基层医院用户可以选择目标医院
function initTownHospitalUI() {
    // 显示医院选择下拉框
    document.getElementById('hospitalSelection').style.display = 'block';
    
    // 加载可选医院列表
    loadHospitals();
}
```

#### 上级医院用户界面
```javascript
// 上级医院用户只能看到发给自己的转诊
function initUpperHospitalUI() {
    // 隐藏医院选择（因为不能创建转诊）
    document.getElementById('hospitalSelection').style.display = 'none';
    
    // 只显示转诊处理功能
    document.getElementById('referralActions').style.display = 'block';
}
```

## 样式建议

### CSS 样式
```css
.hospital-selection {
    margin-bottom: 20px;
}

.hospital-badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.hospital-maternity {
    background-color: #e91e63;
    color: white;
}

.hospital-peoples {
    background-color: #2196f3;
    color: white;
}

.hospital-tcm {
    background-color: #4caf50;
    color: white;
}
```

## 验证规则

### 前端验证
```javascript
function validateReferralForm() {
    const patientName = document.getElementById('patientName').value.trim();
    const patientAge = document.getElementById('patientAge').value;
    const toHospital = document.getElementById('toHospital').value;
    
    if (!patientName) {
        alert('请输入患者姓名');
        return false;
    }
    
    if (!patientAge || patientAge < 0 || patientAge > 150) {
        alert('请输入有效的患者年龄');
        return false;
    }
    
    if (!toHospital) {
        alert('请选择目标医院');
        return false;
    }
    
    return true;
}
```

## 注意事项

1. **权限控制**: 确保只有基层医院可以创建转诊申请
2. **数据验证**: 前端和后端都要验证医院选择的有效性
3. **用户体验**: 根据用户角色显示不同的界面元素
4. **错误处理**: 提供清晰的错误提示信息
5. **响应式设计**: 确保在移动设备上也能正常使用

## 测试建议

1. 测试不同角色用户的界面显示
2. 测试医院选择功能的正确性
3. 测试转诊申请的完整流程
4. 测试权限控制是否生效
5. 测试错误情况的处理
