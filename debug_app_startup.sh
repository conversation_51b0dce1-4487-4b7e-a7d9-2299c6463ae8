#!/bin/bash

# 深度调试应用启动问题

APP_DIR="/var/www/referral"
SERVICE_NAME="referral-system"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

echo "🔍 深度调试应用启动问题..."

# 1. 停止supervisor服务
print_status "停止Supervisor服务..."
supervisorctl stop $SERVICE_NAME || true

# 2. 检查应用目录和文件
print_info "=== 检查应用文件 ==="
cd "$APP_DIR"
pwd
ls -la

echo ""
print_info "检查关键文件:"
for file in app.py config.py requirements.txt .env; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
    fi
done

# 3. 检查Python环境
print_info "=== 检查Python环境 ==="
echo "Python版本:"
sudo -u referral venv/bin/python --version

echo ""
echo "已安装的包:"
sudo -u referral venv/bin/pip list | grep -E "(Flask|SQLAlchemy|PyMySQL|JWT)"

# 4. 检查环境变量
print_info "=== 检查环境变量 ==="
if [ -f ".env" ]; then
    echo "环境变量文件内容:"
    cat .env
else
    print_warning "环境变量文件不存在"
fi

# 5. 测试数据库连接
print_info "=== 测试数据库连接 ==="
echo "测试MySQL连接..."
if mysql -h ************* -u root -pwindows1 -e "SELECT 1;" 2>/dev/null; then
    echo "✅ 数据库连接成功"
    
    echo "检查数据库是否存在:"
    mysql -h ************* -u root -pwindows1 -e "SHOW DATABASES;" | grep -E "(referral_system|user_system)" || echo "❌ 数据库不存在"
else
    echo "❌ 数据库连接失败"
fi

# 6. 手动测试Python应用启动
print_info "=== 手动测试应用启动 ==="
echo "尝试手动启动应用..."

# 创建测试脚本
cat > test_app.py << 'EOF'
#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, '/var/www/referral')

try:
    print("1. 导入基础模块...")
    import pymysql
    print("✅ pymysql导入成功")
    
    from flask import Flask
    print("✅ Flask导入成功")
    
    print("2. 测试数据库连接...")
    connection = pymysql.connect(
        host='*************',
        user='root',
        password='windows1',
        port=3306
    )
    connection.close()
    print("✅ 数据库连接测试成功")
    
    print("3. 导入应用模块...")
    from app import create_app
    print("✅ 应用模块导入成功")
    
    print("4. 创建应用实例...")
    app = create_app('production')
    print("✅ 应用实例创建成功")
    
    print("5. 测试应用配置...")
    with app.app_context():
        print(f"数据库URI: {app.config.get('SQLALCHEMY_DATABASE_URI', 'Not set')}")
        print("✅ 应用配置正常")
    
    print("🎉 应用启动测试成功！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 应用启动错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF

echo "运行应用启动测试..."
sudo -u referral venv/bin/python test_app.py

# 7. 检查具体的错误日志
print_info "=== 检查详细错误日志 ==="

# 清空日志文件
> logs/app.log

# 尝试启动应用并立即查看日志
echo "启动应用并查看实时日志..."
supervisorctl start $SERVICE_NAME &
sleep 2
supervisorctl status $SERVICE_NAME

echo ""
echo "应用日志内容:"
if [ -f "logs/app.log" ]; then
    cat logs/app.log
else
    echo "日志文件为空或不存在"
fi

# 8. 检查supervisor日志
print_info "=== 检查Supervisor日志 ==="
echo "Supervisor错误日志:"
if [ -f "/var/log/supervisor/supervisord.log" ]; then
    tail -20 /var/log/supervisor/supervisord.log | grep -A5 -B5 referral-system || echo "没有找到相关日志"
fi

# 9. 尝试不同的启动方式
print_info "=== 尝试直接启动应用 ==="
echo "尝试直接运行app.py..."

# 设置环境变量并直接运行
export FLASK_ENV=production
timeout 10s sudo -u referral -E venv/bin/python app.py &
APP_PID=$!
sleep 3

if kill -0 $APP_PID 2>/dev/null; then
    echo "✅ 应用可以直接启动"
    kill $APP_PID
else
    echo "❌ 应用直接启动也失败"
fi

# 10. 检查端口占用
print_info "=== 检查端口占用 ==="
echo "检查5000端口是否被占用:"
netstat -tlnp | grep 5000 || echo "端口5000未被占用"

# 11. 提供修复建议
print_info "=== 修复建议 ==="
echo ""
echo "基于以上诊断，可能的解决方案："
echo ""
echo "1. 如果是导入错误，重新安装依赖:"
echo "   cd $APP_DIR"
echo "   sudo -u referral venv/bin/pip install -r requirements.txt --force-reinstall"
echo ""
echo "2. 如果是数据库问题，创建数据库:"
echo "   mysql -h ************* -u root -pwindows1 -e \"CREATE DATABASE IF NOT EXISTS referral_system;\""
echo "   mysql -h ************* -u root -pwindows1 -e \"CREATE DATABASE IF NOT EXISTS user_system;\""
echo ""
echo "3. 如果是配置问题，检查.env文件"
echo ""
echo "4. 如果是权限问题:"
echo "   sudo chown -R referral:referral $APP_DIR"
echo "   sudo chmod -R 755 $APP_DIR"
echo ""
echo "5. 重新部署应用:"
echo "   sudo ./deploy_simple.sh"

# 清理测试文件
rm -f test_app.py

print_status "调试完成。请根据上述输出确定具体问题。"
