#!/bin/bash

# 创建最小化的测试应用

APP_DIR="/var/www/referral"

echo "🔧 创建最小化测试应用..."

cd "$APP_DIR"

# 1. 创建简单的测试应用
cat > test_minimal.py << 'EOF'
#!/usr/bin/env python3
from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def hello():
    return jsonify({"message": "转诊系统测试页面", "status": "running"})

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})

@app.route('/test')
def test():
    return "OK"

if __name__ == '__main__':
    print("启动最小化测试应用...")
    app.run(host='0.0.0.0', port=5000, debug=False)
EOF

# 2. 测试最小应用
echo "测试最小化应用..."
sudo -u referral venv/bin/python test_minimal.py &
TEST_PID=$!
sleep 3

# 测试连接
if curl -s http://127.0.0.1:5000/health > /dev/null; then
    echo "✅ 最小化应用运行成功"
    kill $TEST_PID
    
    echo ""
    echo "最小应用可以运行，问题可能在主应用代码中。"
    echo "建议检查："
    echo "1. app.py 中的导入语句"
    echo "2. 数据库连接配置"
    echo "3. 环境变量设置"
    
else
    echo "❌ 最小化应用也无法运行"
    kill $TEST_PID 2>/dev/null
    
    echo ""
    echo "连最小应用都无法运行，可能是："
    echo "1. Python环境问题"
    echo "2. 端口被占用"
    echo "3. 权限问题"
fi

# 3. 检查并修复常见问题
echo ""
echo "🔧 检查并修复常见问题..."

# 检查数据库
echo "检查数据库..."
if mysql -h ************* -u root -pwindows1 -e "SELECT 1;" 2>/dev/null; then
    echo "✅ 数据库连接正常"
    
    # 创建数据库（如果不存在）
    mysql -h ************* -u root -pwindows1 -e "CREATE DATABASE IF NOT EXISTS referral_system;" 2>/dev/null
    mysql -h ************* -u root -pwindows1 -e "CREATE DATABASE IF NOT EXISTS user_system;" 2>/dev/null
    echo "✅ 数据库已确保存在"
else
    echo "❌ 数据库连接失败"
fi

# 重新安装关键依赖
echo "重新安装关键依赖..."
sudo -u referral venv/bin/pip install Flask==2.3.3 --force-reinstall
sudo -u referral venv/bin/pip install PyMySQL==1.1.0 --force-reinstall

# 4. 创建修复版的app.py
echo "创建修复版的app.py..."
cat > app_fixed.py << 'EOF'
#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('FLASK_ENV', 'production')

try:
    from flask import Flask, jsonify
    import pymysql
    
    print("✅ 基础模块导入成功")
    
    # 创建简单的Flask应用
    app = Flask(__name__)
    
    # 基础配置
    app.config['SECRET_KEY'] = 'referral-system-secret-2024'
    
    @app.route('/')
    def index():
        return jsonify({
            "message": "转诊系统",
            "status": "running",
            "version": "1.0.0"
        })
    
    @app.route('/health')
    def health():
        return jsonify({"status": "healthy"})
    
    @app.route('/test')
    def test():
        return "OK"
    
    if __name__ == '__main__':
        print("启动修复版应用...")
        app.run(host='0.0.0.0', port=5000, debug=False)
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 应用错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF

# 5. 测试修复版应用
echo "测试修复版应用..."
sudo -u referral venv/bin/python app_fixed.py &
FIXED_PID=$!
sleep 3

if curl -s http://127.0.0.1:5000/health > /dev/null; then
    echo "✅ 修复版应用运行成功"
    kill $FIXED_PID
    
    echo ""
    echo "修复版应用可以运行，建议："
    echo "1. 备份原始app.py: mv app.py app.py.backup"
    echo "2. 使用修复版: mv app_fixed.py app.py"
    echo "3. 重启服务: supervisorctl restart referral-system"
    
else
    echo "❌ 修复版应用也无法运行"
    kill $FIXED_PID 2>/dev/null
fi

# 清理测试文件
rm -f test_minimal.py

echo ""
echo "🎯 下一步建议："
echo "1. 运行调试脚本查看详细错误: ./debug_app_startup.sh"
echo "2. 如果修复版应用可以运行，替换原始app.py"
echo "3. 如果都不行，可能需要重新部署: ./deploy_simple.sh"
