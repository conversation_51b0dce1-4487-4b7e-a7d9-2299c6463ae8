import os

class Config:
    # JWT配置
    JWT_SECRET_KEY = 'super-secret-key'  # 生产环境应使用环境变量
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1小时
    JWT_ERROR_MESSAGE_KEY = 'msg'
    JWT_IDENTITY_CLAIM = 'sub'
    JWT_HEADER_TYPE = 'Bearer'

    # 数据库基本配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_POOL_SIZE = 10
    SQLALCHEMY_POOL_TIMEOUT = 30
    SQLALCHEMY_POOL_RECYCLE = 1800

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:windows1@1.94.233.206:3306/referral_system'
    SQLALCHEMY_BINDS = {
        'users': 'mysql+pymysql://root:windows1@1.94.233.206:3306/user_system'
    }

class ProductionConfig(Config):
    DEBUG = False
    # 生产环境应从环境变量获取数据库配置
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 
        'mysql+pymysql://root:windows1@1.94.233.206:3306/referral_system')
    SQLALCHEMY_BINDS = {
        'users': os.getenv('USER_DATABASE_URL',
            'mysql+pymysql://root:windows1@1.94.233.206:3306/user_system')
    }
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'super-secret-key')

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:windows1@1.94.233.206:3306/referral_system_test'
    SQLALCHEMY_BINDS = {
        'users': 'mysql+pymysql://root:windows1@1.94.233.206:3306/user_system_test'
    }

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
} 