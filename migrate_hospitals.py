#!/usr/bin/env python3
"""
数据库迁移脚本：更新医院配置
将现有的"上级医院"转诊记录更新为具体的医院名称
"""

import pymysql
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'windows1',
    'port': 3306,
    'database': 'referral_system',
    'charset': 'utf8mb4'
}

# 医院配置
HOSPITALS = {
    'maternity': '涟源市妇幼保健院',
    'peoples': '涟源市人民医院', 
    'tcm': '涟源市中医院'
}

UPPER_HOSPITALS = list(HOSPITALS.values())

def connect_database():
    """连接数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def update_referral_hospitals(connection):
    """更新转诊记录中的医院信息"""
    try:
        with connection.cursor() as cursor:
            # 查询所有"上级医院"的转诊记录
            cursor.execute("""
                SELECT id, from_hospital, to_hospital 
                FROM referral 
                WHERE to_hospital = '上级医院'
            """)
            
            referrals = cursor.fetchall()
            logger.info(f"找到 {len(referrals)} 条需要更新的转诊记录")
            
            if not referrals:
                logger.info("没有需要更新的记录")
                return
            
            # 为每个转诊记录分配一个具体的医院
            # 这里使用简单的轮询分配策略
            updated_count = 0
            
            for i, (referral_id, from_hospital, to_hospital) in enumerate(referrals):
                # 轮询分配医院
                target_hospital = UPPER_HOSPITALS[i % len(UPPER_HOSPITALS)]
                
                # 更新记录
                cursor.execute("""
                    UPDATE referral 
                    SET to_hospital = %s 
                    WHERE id = %s
                """, (target_hospital, referral_id))
                
                updated_count += 1
                logger.info(f"更新转诊记录 {referral_id}: {from_hospital} -> {target_hospital}")
            
            # 提交更改
            connection.commit()
            logger.info(f"成功更新 {updated_count} 条转诊记录")
            
    except Exception as e:
        connection.rollback()
        logger.error(f"更新转诊记录失败: {e}")
        raise

def create_hospital_users(connection):
    """创建医院用户账户（如果不存在）"""
    try:
        with connection.cursor() as cursor:
            # 切换到用户数据库
            cursor.execute("USE user_system")
            
            # 检查并创建医院用户
            for hospital_key, hospital_name in HOSPITALS.items():
                # 检查用户是否已存在
                cursor.execute("""
                    SELECT id FROM users 
                    WHERE hospital_name = %s AND role = 'hospital'
                """, (hospital_name,))
                
                if cursor.fetchone():
                    logger.info(f"医院用户已存在: {hospital_name}")
                    continue
                
                # 创建新的医院用户
                username = f"{hospital_key}_admin"
                # 使用werkzeug生成密码哈希
                from werkzeug.security import generate_password_hash
                password_hash = generate_password_hash("123456")  # 默认密码
                
                cursor.execute("""
                    INSERT INTO users (username, password_hash, hospital_name, role, fullName, phone, created_at)
                    VALUES (%s, %s, %s, 'hospital', %s, '', %s)
                """, (
                    username,
                    password_hash,
                    hospital_name,
                    f"{hospital_name}管理员",
                    datetime.now()
                ))
                
                logger.info(f"创建医院用户: {username} ({hospital_name})")
            
            connection.commit()
            logger.info("医院用户创建完成")
            
    except Exception as e:
        connection.rollback()
        logger.error(f"创建医院用户失败: {e}")
        raise

def main():
    """主函数"""
    logger.info("开始数据库迁移...")
    
    connection = None
    try:
        # 连接数据库
        connection = connect_database()
        
        # 更新转诊记录
        update_referral_hospitals(connection)
        
        # 创建医院用户
        create_hospital_users(connection)
        
        logger.info("数据库迁移完成！")
        
        # 输出医院用户信息
        print("\n" + "="*50)
        print("医院用户账户信息:")
        print("="*50)
        for hospital_key, hospital_name in HOSPITALS.items():
            username = f"{hospital_key}_admin"
            print(f"医院: {hospital_name}")
            print(f"用户名: {username}")
            print(f"密码: 123456")
            print("-" * 30)
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        return 1
    finally:
        if connection:
            connection.close()
            logger.info("数据库连接已关闭")
    
    return 0

if __name__ == "__main__":
    exit(main())
