#!/bin/bash

# 修复域名和SSL问题的脚本

set -e

DOMAIN="referral.beimoyinhenlinlin.cn"
SERVER_IP="*************"
APP_DIR="/var/www/referral"
SERVICE_NAME="referral-system"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root权限运行此脚本"
        exit 1
    fi
}

# 检查DNS解析
check_dns() {
    print_status "检查DNS解析..."
    
    DNS_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
    if [ "$DNS_IP" != "$SERVER_IP" ]; then
        print_error "DNS解析错误！"
        echo "当前解析到: $DNS_IP"
        echo "应该解析到: $SERVER_IP"
        echo ""
        echo "请先修复DNS解析："
        echo "1. 登录域名管理面板"
        echo "2. 修改A记录，将 $DOMAIN 指向 $SERVER_IP"
        echo "3. 等待DNS传播后重新运行此脚本"
        exit 1
    fi
    
    print_status "✅ DNS解析正确"
}

# 停止可能冲突的服务
stop_conflicting_services() {
    print_status "停止可能冲突的服务..."
    
    # 停止Apache（如果运行）
    systemctl stop apache2 2>/dev/null || true
    systemctl disable apache2 2>/dev/null || true
    
    # 停止其他可能占用80/443端口的服务
    fuser -k 80/tcp 2>/dev/null || true
    fuser -k 443/tcp 2>/dev/null || true
    
    print_status "冲突服务停止完成"
}

# 重新配置Nginx
reconfigure_nginx() {
    print_status "重新配置Nginx..."
    
    # 删除现有配置
    rm -f /etc/nginx/sites-enabled/$DOMAIN
    rm -f /etc/nginx/sites-available/$DOMAIN
    
    # 创建新的Nginx配置（仅HTTP，SSL稍后配置）
    cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # 静态文件
    location /static/ {
        alias $APP_DIR/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads/ {
        alias $APP_DIR/static/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        access_log off;
    }
    
    # 测试端点
    location /test {
        proxy_pass http://127.0.0.1:5000/test;
        access_log off;
    }
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
    
    # 删除默认站点
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t
    
    print_status "Nginx配置完成"
}

# 确保应用正在运行
ensure_app_running() {
    print_status "确保应用正在运行..."
    
    # 检查应用状态
    if ! supervisorctl status $SERVICE_NAME | grep -q "RUNNING"; then
        print_status "启动应用..."
        supervisorctl start $SERVICE_NAME
        sleep 5
    fi
    
    # 验证应用是否响应
    if curl -s http://127.0.0.1:5000/health > /dev/null; then
        print_status "✅ 应用运行正常"
    else
        print_error "❌ 应用未正常响应"
        echo "请检查应用日志: tail -f $APP_DIR/logs/app.log"
        exit 1
    fi
}

# 重启Nginx
restart_nginx() {
    print_status "重启Nginx..."
    
    systemctl restart nginx
    systemctl enable nginx
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        print_status "✅ Nginx运行正常"
    else
        print_error "❌ Nginx启动失败"
        systemctl status nginx
        exit 1
    fi
}

# 测试HTTP访问
test_http_access() {
    print_status "测试HTTP访问..."
    
    sleep 3  # 等待服务稳定
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN/health)
    if [ "$HTTP_STATUS" = "200" ]; then
        print_status "✅ HTTP访问正常"
        echo "可以访问: http://$DOMAIN"
    else
        print_warning "⚠️  HTTP访问异常，状态码: $HTTP_STATUS"
    fi
}

# 安装和配置SSL证书
setup_ssl() {
    print_status "设置SSL证书..."
    
    # 安装certbot
    if ! command -v certbot >/dev/null 2>&1; then
        print_status "安装certbot..."
        apt update
        apt install -y certbot python3-certbot-nginx
    fi
    
    # 删除现有证书（如果存在且有问题）
    if certbot certificates 2>/dev/null | grep -q "$DOMAIN"; then
        print_status "删除现有证书..."
        certbot delete --cert-name $DOMAIN --non-interactive
    fi
    
    # 获取新证书
    print_status "获取SSL证书..."
    if certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN; then
        print_status "✅ SSL证书配置成功"
        
        # 测试HTTPS访问
        sleep 5
        HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/health)
        if [ "$HTTPS_STATUS" = "200" ]; then
            print_status "✅ HTTPS访问正常"
            echo "可以访问: https://$DOMAIN"
        else
            print_warning "⚠️  HTTPS访问可能需要等待几分钟"
        fi
    else
        print_error "❌ SSL证书配置失败"
        echo "可能的原因："
        echo "1. DNS还未完全传播"
        echo "2. 防火墙阻止了80/443端口"
        echo "3. 域名解析问题"
        echo ""
        echo "请稍后重试或手动运行: certbot --nginx -d $DOMAIN"
    fi
}

# 设置自动续期
setup_auto_renewal() {
    print_status "设置SSL证书自动续期..."
    
    # 添加cron任务
    CRON_JOB="0 2 1 * * /usr/bin/certbot renew --nginx --quiet && /bin/systemctl reload nginx"
    (crontab -l 2>/dev/null | grep -v certbot; echo "$CRON_JOB") | crontab -
    
    print_status "SSL证书自动续期设置完成"
}

# 显示最终状态
show_final_status() {
    print_status "显示最终状态..."
    
    echo ""
    echo "🎉 域名和SSL修复完成！"
    echo ""
    echo "访问地址:"
    echo "  HTTP:  http://$DOMAIN"
    echo "  HTTPS: https://$DOMAIN"
    echo ""
    echo "健康检查:"
    echo "  HTTP:  http://$DOMAIN/health"
    echo "  HTTPS: https://$DOMAIN/health"
    echo ""
    echo "管理命令:"
    echo "  查看应用状态: supervisorctl status $SERVICE_NAME"
    echo "  查看Nginx状态: systemctl status nginx"
    echo "  查看SSL证书: certbot certificates"
    echo ""
    echo "默认医院管理员账户:"
    echo "  涟源市妇幼保健院: maternity_admin / 123456"
    echo "  涟源市人民医院: peoples_admin / 123456"
    echo "  涟源市中医院: tcm_admin / 123456"
    echo ""
    print_warning "⚠️  请立即修改默认密码！"
}

# 主函数
main() {
    print_status "开始修复域名和SSL问题..."
    
    check_root
    check_dns
    stop_conflicting_services
    reconfigure_nginx
    ensure_app_running
    restart_nginx
    test_http_access
    
    # 询问是否配置SSL
    echo ""
    read -p "是否现在配置SSL证书？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_ssl
        setup_auto_renewal
    else
        print_status "跳过SSL配置，稍后可以运行: certbot --nginx -d $DOMAIN"
    fi
    
    show_final_status
}

# 执行主函数
main "$@"
