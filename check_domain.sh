#!/bin/bash

# 域名和SSL检查脚本

DOMAIN="referral.beimoyinhenlinlin.cn"
SERVER_IP="*************"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo "🔍 检查域名和SSL状态..."
echo "域名: $DOMAIN"
echo "目标服务器: $SERVER_IP"
echo ""

# 1. 检查DNS解析
print_info "=== DNS解析检查 ==="
DNS_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
if [ -n "$DNS_IP" ]; then
    echo "域名解析到: $DNS_IP"
    if [ "$DNS_IP" = "$SERVER_IP" ]; then
        print_status "✅ DNS解析正确"
    else
        print_error "❌ DNS解析错误 - 应该指向 $SERVER_IP"
        echo "请修改DNS A记录，将 $DOMAIN 指向 $SERVER_IP"
    fi
else
    print_error "❌ 无法解析域名"
fi
echo ""

# 2. 检查端口连通性
print_info "=== 端口连通性检查 ==="
if timeout 5 bash -c "</dev/tcp/$SERVER_IP/80" 2>/dev/null; then
    print_status "✅ 端口80可达"
else
    print_error "❌ 端口80不可达"
fi

if timeout 5 bash -c "</dev/tcp/$SERVER_IP/443" 2>/dev/null; then
    print_status "✅ 端口443可达"
else
    print_warning "⚠️  端口443不可达（可能未配置SSL）"
fi
echo ""

# 3. 检查HTTP响应
print_info "=== HTTP响应检查 ==="
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN/ 2>/dev/null || echo "000")
echo "HTTP状态码: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "200" ]; then
    print_status "✅ HTTP访问正常"
elif [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    print_warning "⚠️  HTTP重定向 (可能重定向到HTTPS)"
else
    print_error "❌ HTTP访问异常"
fi
echo ""

# 4. 检查HTTPS和SSL证书
print_info "=== HTTPS和SSL检查 ==="
if command -v openssl >/dev/null 2>&1; then
    echo "检查SSL证书..."
    SSL_INFO=$(echo | timeout 10 openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject -dates 2>/dev/null)
    
    if [ -n "$SSL_INFO" ]; then
        echo "SSL证书信息:"
        echo "$SSL_INFO"
        
        # 检查证书中的域名
        CERT_CN=$(echo "$SSL_INFO" | grep "subject=" | sed 's/.*CN = //' | sed 's/,.*//')
        echo "证书通用名称: $CERT_CN"
        
        if [ "$CERT_CN" = "$DOMAIN" ]; then
            print_status "✅ SSL证书域名匹配"
        else
            print_error "❌ SSL证书域名不匹配"
            echo "证书是为 '$CERT_CN' 签发的，但访问的是 '$DOMAIN'"
        fi
    else
        print_error "❌ 无法获取SSL证书信息"
    fi
else
    print_warning "⚠️  openssl命令不可用，跳过SSL检查"
fi
echo ""

# 5. 检查服务器响应内容
print_info "=== 服务器响应内容检查 ==="
echo "尝试获取服务器响应..."
RESPONSE=$(curl -s -L http://$DOMAIN/ 2>/dev/null | head -c 200)
if [ -n "$RESPONSE" ]; then
    echo "响应内容预览:"
    echo "$RESPONSE"
    
    # 检查是否是转诊系统
    if echo "$RESPONSE" | grep -qi "转诊\|referral"; then
        print_status "✅ 似乎是转诊系统"
    else
        print_warning "⚠️  响应内容不像转诊系统"
    fi
else
    print_error "❌ 无法获取服务器响应"
fi
echo ""

# 6. 建议的解决方案
print_info "=== 建议的解决方案 ==="
echo "基于检查结果，建议："
echo ""

if [ "$DNS_IP" != "$SERVER_IP" ]; then
    echo "1. 🔧 修复DNS解析:"
    echo "   - 登录域名管理面板"
    echo "   - 修改A记录，将 $DOMAIN 指向 $SERVER_IP"
    echo "   - 等待DNS传播（可能需要几分钟到几小时）"
    echo ""
fi

echo "2. 🔧 修复SSL证书:"
echo "   - 在服务器上运行: sudo certbot --nginx -d $DOMAIN"
echo "   - 或删除现有证书后重新申请: sudo certbot delete --cert-name $DOMAIN"
echo ""

echo "3. 🔧 确保Nginx配置正确:"
echo "   - 检查配置: sudo nginx -t"
echo "   - 重启Nginx: sudo systemctl restart nginx"
echo ""

echo "4. 🔧 检查应用是否运行:"
echo "   - 检查应用状态: sudo supervisorctl status referral-system"
echo "   - 查看应用日志: sudo tail -f /var/www/referral/logs/app.log"
echo ""

echo "完成这些步骤后，重新运行此脚本检查状态。"
