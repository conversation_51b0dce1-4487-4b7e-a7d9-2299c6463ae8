#!/bin/bash

# 转诊系统生产环境部署脚本
# 服务器: *************
# 域名: https://referral.beimoyinhenlinlin.cn

set -e  # 遇到错误立即退出

echo "🚀 开始部署转诊系统到生产环境..."

# 配置变量
SERVER_IP="*************"
DOMAIN="referral.beimoyinhenlinlin.cn"
APP_DIR="/var/www/referral"
SERVICE_NAME="referral-system"
PYTHON_VERSION="3.9"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在服务器上运行
check_environment() {
    print_status "检查部署环境..."
    
    if [ ! -f /etc/os-release ]; then
        print_error "不是Linux系统，请在目标服务器上运行此脚本"
        exit 1
    fi
    
    # 检查是否有sudo权限
    if ! sudo -n true 2>/dev/null; then
        print_error "需要sudo权限来安装系统包"
        exit 1
    fi
    
    print_status "环境检查通过"
}

# 安装系统依赖
install_system_dependencies() {
    print_status "安装系统依赖..."
    
    # 更新包管理器
    sudo apt update
    
    # 安装基础依赖
    sudo apt install -y \
        python3 \
        python3-pip \
        python3-venv \
        nginx \
        mysql-client \
        supervisor \
        git \
        curl \
        wget \
        unzip
    
    # 安装SSL证书工具
    sudo apt install -y certbot python3-certbot-nginx
    
    print_status "系统依赖安装完成"
}

# 创建应用目录和用户
setup_application() {
    print_status "设置应用环境..."
    
    # 创建应用用户
    if ! id "referral" &>/dev/null; then
        sudo useradd -r -s /bin/false referral
        print_status "创建应用用户: referral"
    fi
    
    # 创建应用目录
    sudo mkdir -p $APP_DIR
    sudo mkdir -p $APP_DIR/logs
    sudo mkdir -p $APP_DIR/static/uploads
    
    # 设置目录权限
    sudo chown -R referral:referral $APP_DIR
    sudo chmod -R 755 $APP_DIR
    
    print_status "应用环境设置完成"
}

# 部署应用代码
deploy_application() {
    print_status "部署应用代码..."
    
    # 复制应用文件
    sudo cp -r . $APP_DIR/
    
    # 创建Python虚拟环境
    sudo -u referral python3 -m venv $APP_DIR/venv
    
    # 安装Python依赖
    sudo -u referral $APP_DIR/venv/bin/pip install --upgrade pip
    sudo -u referral $APP_DIR/venv/bin/pip install -r $APP_DIR/requirements.txt
    
    # 设置环境变量
    sudo tee $APP_DIR/.env > /dev/null <<EOF
FLASK_ENV=production
DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/referral_system
USER_DATABASE_URL=mysql+pymysql://root:windows1@*************:3306/user_system
JWT_SECRET_KEY=referral-system-jwt-secret-2024
EOF
    
    sudo chown referral:referral $APP_DIR/.env
    sudo chmod 600 $APP_DIR/.env
    
    print_status "应用代码部署完成"
}

# 配置Nginx
configure_nginx() {
    print_status "配置Nginx..."
    
    # 创建Nginx配置文件
    sudo tee /etc/nginx/sites-available/$DOMAIN > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL配置（证书路径将在获取证书后自动配置）
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 静态文件
    location /static/ {
        alias $APP_DIR/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads/ {
        alias $APP_DIR/static/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:5000/test;
        access_log off;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
    
    # 删除默认站点
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试Nginx配置
    sudo nginx -t
    
    print_status "Nginx配置完成"
}

# 配置Supervisor
configure_supervisor() {
    print_status "配置Supervisor..."
    
    # 创建Supervisor配置文件
    sudo tee /etc/supervisor/conf.d/$SERVICE_NAME.conf > /dev/null <<EOF
[program:$SERVICE_NAME]
command=$APP_DIR/venv/bin/python app.py
directory=$APP_DIR
user=referral
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$APP_DIR/logs/app.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=FLASK_ENV=production
EOF
    
    print_status "Supervisor配置完成"
}

# 获取SSL证书
setup_ssl() {
    print_status "设置SSL证书..."
    
    # 临时启动Nginx（用于证书验证）
    sudo systemctl start nginx
    
    # 获取Let's Encrypt证书
    sudo certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN
    
    print_status "SSL证书设置完成"
}

# 启动服务
start_services() {
    print_status "启动服务..."
    
    # 重新加载Supervisor配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    # 启动应用
    sudo supervisorctl start $SERVICE_NAME
    
    # 重启Nginx
    sudo systemctl restart nginx
    
    # 设置开机自启
    sudo systemctl enable nginx
    sudo systemctl enable supervisor
    
    print_status "服务启动完成"
}

# 运行数据库迁移
run_migrations() {
    print_status "运行数据库迁移..."
    
    cd $APP_DIR
    sudo -u referral $APP_DIR/venv/bin/python migrate_hospitals.py
    
    print_status "数据库迁移完成"
}

# 检查部署状态
check_deployment() {
    print_status "检查部署状态..."
    
    # 检查应用状态
    if sudo supervisorctl status $SERVICE_NAME | grep -q "RUNNING"; then
        print_status "✅ 应用服务运行正常"
    else
        print_error "❌ 应用服务未运行"
        sudo supervisorctl status $SERVICE_NAME
    fi
    
    # 检查Nginx状态
    if sudo systemctl is-active --quiet nginx; then
        print_status "✅ Nginx服务运行正常"
    else
        print_error "❌ Nginx服务未运行"
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":5000"; then
        print_status "✅ 应用端口5000监听正常"
    else
        print_warning "⚠️  应用端口5000未监听"
    fi
    
    if netstat -tlnp | grep -q ":443"; then
        print_status "✅ HTTPS端口443监听正常"
    else
        print_warning "⚠️  HTTPS端口443未监听"
    fi
    
    print_status "部署状态检查完成"
}

# 主部署流程
main() {
    print_status "开始转诊系统部署流程"
    
    check_environment
    install_system_dependencies
    setup_application
    deploy_application
    configure_nginx
    configure_supervisor
    setup_ssl
    start_services
    run_migrations
    check_deployment
    
    echo ""
    print_status "🎉 部署完成！"
    echo ""
    echo "访问地址: https://$DOMAIN"
    echo "管理命令:"
    echo "  查看应用日志: sudo tail -f $APP_DIR/logs/app.log"
    echo "  重启应用: sudo supervisorctl restart $SERVICE_NAME"
    echo "  查看应用状态: sudo supervisorctl status $SERVICE_NAME"
    echo "  重启Nginx: sudo systemctl restart nginx"
    echo ""
    print_status "默认医院管理员账户:"
    echo "  涟源市妇幼保健院: maternity_admin / 123456"
    echo "  涟源市人民医院: peoples_admin / 123456"
    echo "  涟源市中医院: tcm_admin / 123456"
}

# 执行主流程
main "$@"
